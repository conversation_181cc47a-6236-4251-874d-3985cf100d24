# CleanConnect - Mobile App

**Digitalizing Domestic Services with a Centralized, Role-Based Mobile App for The Gambia**

CleanConnect is a modern on-demand home service platform designed to seamlessly connect homeowners with trusted, local service providers for tasks like cleaning and laundry. Our mission is to professionalize and digitize domestic work in emerging markets, starting with The Gambia.

## 🛠️ Tech Stack

- **Frontend**: React Native (TypeScript, Expo)
- **Authentication**: Supabase Auth (OTP-based: Phone Number/Email+Password)
- **Backend**: ExpressJS, Prisma ORM, PostgreSQL
- **Realtime**: Supabase Realtime (Job Broadcasting), Supabase Storage
- **State Management**: Zustand
- **Navigation**: Expo Router

## 🚀 Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn
- Expo CLI
- iOS Simulator (for iOS development) or Android Emulator (for Android development)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd CleanConnect
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env` file in the root directory:
   ```env
   EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
   EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```
   
   **Note**: If environment variables are not set, the app will run in development mode and simulate OTP verification for testing purposes.

4. **Start the development server**
   ```bash
   npm start
   ```

5. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app on your phone

## 📱 App Features

### Customer Features
- **Onboarding**: 3-slide introduction with role selection
- **Home Screen**: Browse services, quick booking shortcuts, popular deals
- **Booking Process**: Select service → Date/Time → Address → Choose Provider → Confirmation
- **Authentication**: OTP-based phone number verification
- **Booking Management**: Track bookings and submit reviews

### Provider Features
- **Onboarding**: Role selection and profile setup
- **Authentication**: Email and password-based login
- **Dashboard**: View available jobs and manage bookings
- **Profile Management**: Update availability and service areas

## 🏗️ Project Structure

```
src/
├── assets/                 # Images, fonts, etc.
├── constants/              # App constants (colors, spacing, typography)
├── components/             # Reusable components
│   ├── common/            # Shared components (Button, Input, etc.)
│   └── bookings/          # Booking-specific components
├── features/              # Feature-based modules
│   ├── auth/              # Authentication screens and logic
│   ├── customer/          # Customer-specific features
│   │   ├── components/    # Customer components
│   │   ├── screens/       # Customer screens
│   │   ├── services/      # Customer services
│   │   └── styles/        # Customer styles
│   └── provider/          # Provider-specific features
├── lib/                   # External library configurations
├── store/                 # Zustand stores
└── types/                 # TypeScript type definitions
```

## 🎨 Design System

### Color Palette
- **Primary**: `#6FD1FF` (Sky Blue)
- **Secondary**: `#A2E2BD` (Mint Green)
- **Accent**: `#84D3F2` (Light Cyan)
- **Warning**: `#FFD43B` (Bright Yellow)
- **Success**: `#4CAF50` (Status Indicator)
- **Neutral**: `#F9F9F9` (Background Light)

### Typography
- **Font Sizes**: xs (12px) to display (48px)
- **Font Weights**: Light (300) to Bold (700)
- **Line Heights**: Tight (1.2) to Relaxed (1.75)

### Spacing
- **Scale**: xs (4px) to xxxl (64px)
- **Consistent spacing throughout the app**

## 🔐 Authentication Flow

### Customer Authentication (OTP-based)
1. User enters phone number
2. 6-digit OTP sent via Supabase
3. User verifies OTP
4. Session maintained locally

### Provider Authentication (Email-based)
1. User enters email and password
2. Supabase validates credentials
3. JWT returned and stored
4. Role-based access control

## 📊 State Management

The app uses Zustand for state management with the following stores:

- **useAuthStore**: Manages authentication state and user information
- **useBookingStore**: Manages booking-related state and operations

## 🚀 Development Workflow

1. **Feature Development**: Create feature-specific folders in `src/features/`
2. **Component Development**: Build reusable components in `src/components/`
3. **State Management**: Add new stores in `src/store/` as needed
4. **Navigation**: Update routes in `app/` directory
5. **Styling**: Use the design system constants for consistency

## 📝 Available Scripts

- `npm start` - Start the Expo development server
- `npm run android` - Start the app on Android emulator
- `npm run ios` - Start the app on iOS simulator
- `npm run web` - Start the app in web browser
- `npm run lint` - Run ESLint

## 🔧 Configuration

### Supabase Setup
1. Create a Supabase project
2. Set up authentication providers (phone, email)
3. Configure database schema
4. Set up real-time subscriptions

### Environment Variables
- `EXPO_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `EXPO_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

## 📱 Platform Support

- ✅ iOS (via Expo)
- ✅ Android (via Expo)
- ✅ Web (via Expo)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support, email <EMAIL> or join our Slack channel.

---

**Built with ❤️ for The Gambia**
