import { colors } from './colors';
import { typography } from './typography';
import { spacing } from './spacing';

export const theme = {
  colors,
  typography,
  spacing,
  
  // CleanConnect component styles
  components: {
    // Primary button (Sky Blue theme)
    button: {
      borderRadius: 12,
      minHeight: 48,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      backgroundColor: colors.primary, // Sky Blue
    },

    // Secondary button (Mint Green theme)
    buttonSecondary: {
      borderRadius: 12,
      minHeight: 48,
      paddingHorizontal: spacing.lg,
      paddingVertical: spacing.md,
      backgroundColor: colors.secondary, // Mint Green
      borderWidth: 1,
      borderColor: colors.secondary,
    },

    // Service card styling
    card: {
      borderRadius: 16,
      padding: spacing.lg,
      backgroundColor: colors.white,
      shadowColor: colors.shadow.light,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 1,
      shadowRadius: 4,
      elevation: 2,
    },

    // Service category card
    serviceCard: {
      borderRadius: 16,
      padding: spacing.lg,
      backgroundColor: colors.white,
      borderWidth: 1,
      borderColor: colors.neutral,
      shadowColor: colors.primary,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.1,
      shadowRadius: 4,
      elevation: 2,
    },
    
    input: {
      borderRadius: 12,
      paddingHorizontal: spacing.md,
      paddingVertical: spacing.md,
      borderWidth: 1,
      borderColor: colors.border.light,
      backgroundColor: colors.white,
      fontSize: typography.sizes.md,
      fontFamily: typography.fontFamily.regular,
    },
    
    // CleanConnect screen styling
    screen: {
      flex: 1,
      backgroundColor: colors.neutral, // Updated to use new neutral color
      paddingHorizontal: spacing.xl,
    },

    // Header styling for CleanConnect
    header: {
      backgroundColor: colors.white,
      paddingHorizontal: spacing.xl,
      paddingVertical: spacing.lg,
      borderBottomWidth: 1,
      borderBottomColor: colors.border.light,
    },
  },
  
  // Common shadows
  shadows: {
    small: {
      shadowColor: colors.shadow.light,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 1,
      shadowRadius: 2,
      elevation: 1,
    },
    
    medium: {
      shadowColor: colors.shadow.medium,
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 1,
      shadowRadius: 4,
      elevation: 2,
    },
    
    large: {
      shadowColor: colors.shadow.dark,
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 1,
      shadowRadius: 8,
      elevation: 4,
    },
  },
} as const;
