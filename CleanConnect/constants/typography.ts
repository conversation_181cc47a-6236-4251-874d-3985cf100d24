
import { useFonts } from 'expo-font';

// Font loading hook - use this in your App.tsx or root component
export const useCustomFonts = () => {
  const [fontsLoaded] = useFonts({
    'PublicSans-Light': require('@/assets/fonts/PublicSans-Light.ttf'),
    'PublicSans-Regular': require('@/assets/fonts/PublicSans-Regular.ttf'),
    'PublicSans-Medium': require('@/assets/fonts/PublicSans-Medium.ttf'),
    'PublicSans-SemiBold': require('@/assets/fonts/PublicSans-SemiBold.ttf'),
    'PublicSans-Bold': require('@/assets/fonts/PublicSans-Bold.ttf'),
  });

  return fontsLoaded;
};

// Typography constants
export const typography = {
  fontFamily: {
    light: 'PublicSans-Light',
    regular: 'PublicSans-Regular',
    medium: 'PublicSans-Medium',
    semibold: 'PublicSans-SemiBold',
    bold: 'PublicSans-Bold',
  },
  sizes: {
    xs: 12,
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
    xxl: 24,
    xxxl: 32,
    display: 48,
  },
  weights: {
    light: '300',
    regular: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  // Improved line heights for better readability
  lineHeights: {
    tight: 1.25,    // For headers - improved from 1.3
    normal: 1.5,    // For body text - improved from 1.6
    relaxed: 1.75,  // For dense text - improved from 1.8
    loose: 2.0,     // Added for extra spacing when needed
  },
} as const;

// Helper function to create text styles with better defaults
export const createTextStyle = (
  size: keyof typeof typography.sizes,
  weight: keyof typeof typography.fontFamily,
  lineHeight?: keyof typeof typography.lineHeights
) => ({
  fontFamily: typography.fontFamily[weight],
  fontSize: typography.sizes[size],
  lineHeight: lineHeight ? typography.sizes[size] * typography.lineHeights[lineHeight] : typography.sizes[size] * typography.lineHeights.normal,
});

// Pre-defined text styles optimized for readability
export const textStyles = {
  // Headers - using tight line height for better visual hierarchy
  h1: createTextStyle('display', 'bold', 'tight'),
  h2: createTextStyle('xxxl', 'bold', 'tight'),
  h3: createTextStyle('xxl', 'semibold', 'tight'),
  h4: createTextStyle('xl', 'semibold', 'tight'),
  h5: createTextStyle('lg', 'medium', 'tight'),
  h6: createTextStyle('md', 'medium', 'normal'),
  
  // Body text - using normal line height for optimal readability
  body: createTextStyle('md', 'regular', 'normal'),
  bodyLarge: createTextStyle('lg', 'regular', 'normal'),
  bodySmall: createTextStyle('sm', 'regular', 'normal'),
  
  // Dense text content - using relaxed line height for better scanning
  paragraph: createTextStyle('md', 'regular', 'relaxed'),
  paragraphLarge: createTextStyle('lg', 'regular', 'relaxed'),
  
  // Captions and labels
  caption: createTextStyle('xs', 'regular', 'normal'),
  label: createTextStyle('sm', 'medium', 'normal'),
  labelLarge: createTextStyle('md', 'medium', 'normal'),
  
  // Interactive elements - tighter for better button appearance
  button: createTextStyle('md', 'semibold', 'tight'),
  buttonLarge: createTextStyle('lg', 'semibold', 'tight'),
  buttonSmall: createTextStyle('sm', 'semibold', 'tight'),
  
  // Links - normal line height for inline usage
  link: createTextStyle('md', 'medium', 'normal'),
  linkSmall: createTextStyle('sm', 'medium', 'normal'),
  
  // Additional utility styles for better readability
  subtitle: createTextStyle('lg', 'regular', 'normal'),
  overline: createTextStyle('xs', 'medium', 'normal'),
  
  // Special cases for very readable text
  readableBody: createTextStyle('md', 'regular', 'loose'),
  readableBodyLarge: createTextStyle('lg', 'regular', 'loose'),
} as const;

export type Typography = typeof typography;
export type TextStyle = keyof typeof textStyles;

// Utility function to get line height value directly
export const getLineHeight = (size: keyof typeof typography.sizes, lineHeight: keyof typeof typography.lineHeights) => {
  return typography.sizes[size] * typography.lineHeights[lineHeight];
};

// Utility function for custom text styles with better readability defaults
export const createReadableTextStyle = (
  size: keyof typeof typography.sizes,
  weight: keyof typeof typography.fontFamily,
  customLineHeight?: number
) => ({
  fontFamily: typography.fontFamily[weight],
  fontSize: typography.sizes[size],
  lineHeight: customLineHeight || typography.sizes[size] * typography.lineHeights.normal,
  letterSpacing: typography.sizes[size] < 16 ? 0.5 : 0, // Add letter spacing for small text
});