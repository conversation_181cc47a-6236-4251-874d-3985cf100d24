import { colors } from '@/constants/colors';

export interface ServiceCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  description: string;
  rating: number;
  totalBookings: string;
  images: CategoryImage[];
  subcategories: ServiceSubcategory[];
  promotions: Promotion[];
}

export interface CategoryImage {
  id: string;
  url: string;
  title: string;
}

export interface ServiceSubcategory {
  id: string;
  title: string;
  image: string;
  services: ServiceItem[];
}

export interface ServiceItem {
  id: string;
  name: string;
  description: string;
  rating: number;
  reviewCount: string;
  startingPrice: number;
  originalPrice?: number;
  duration?: string;
  options: number;
  image: string;
  popular?: boolean;
  available?: boolean;
  features?: string[];
}

export interface Promotion {
  id: string;
  title: string;
  subtitle: string;
  icon: string;
  color: string;
}

// Comprehensive service categories data
export const serviceCategories: ServiceCategory[] = [
  {
    id: 'house-cleaning',
    name: 'House Cleaning',
    icon: '🏠',
    color: colors.categories.cleaning,
    description: 'Professional house cleaning services for all your home needs',
    rating: 4.8,
    totalBookings: '2.1M',
    images: [
      { id: 'occupied-apt', url: '🏠', title: 'Occupied apartment' },
      { id: 'unfurnished-apt', url: '🏢', title: 'Unfurnished apartment' },
      { id: 'independent-house', url: '🏘️', title: 'Independent house' },
      { id: 'mini-services', url: '🧹', title: 'Mini services' },
    ],
    subcategories: [
      {
        id: 'occupied-apartment',
        title: 'Occupied apartment',
        image: '🏠',
        services: [
          {
            id: 'premium-deep-cleaning',
            name: 'Premium home deep cleaning',
            description: 'Full deep cleaning of bathrooms and kitchen, including inside trolleys/shelves',
            rating: 4.56,
            reviewCount: '6K',
            startingPrice: 4499,
            originalPrice: 5299,
            duration: '4-6 hours',
            options: 5,
            image: '🧽',
            popular: true,
            features: ['Deep bathroom cleaning', 'Kitchen appliance cleaning', 'Inside cabinet cleaning'],
          },
          {
            id: 'standard-cleaning',
            name: 'Standard home cleaning',
            description: 'Regular cleaning of all rooms, dusting, mopping, and basic kitchen cleaning',
            rating: 4.42,
            reviewCount: '3.2K',
            startingPrice: 2999,
            originalPrice: 3499,
            duration: '2-3 hours',
            options: 3,
            image: '🧹',
            features: ['All room cleaning', 'Dusting and mopping', 'Basic kitchen clean'],
          },
        ],
      },
      {
        id: 'unfurnished-apartment',
        title: 'Unfurnished apartment',
        image: '🏢',
        services: [
          {
            id: 'move-in-cleaning',
            name: 'Move-in deep cleaning',
            description: 'Comprehensive cleaning for new apartments including all surfaces and fixtures',
            rating: 4.65,
            reviewCount: '2.1K',
            startingPrice: 3999,
            originalPrice: 4599,
            duration: '3-4 hours',
            options: 4,
            image: '✨',
            features: ['Complete surface cleaning', 'Fixture cleaning', 'Move-in ready'],
          },
        ],
      },
    ],
    promotions: [
      {
        id: 'cleaning-plus',
        title: 'Save 15% on every order',
        subtitle: 'Get CleanConnect Plus now',
        icon: '💎',
        color: colors.primary,
      },
    ],
  },
  {
    id: 'air-conditioner',
    name: 'Air Conditioner',
    icon: '❄️',
    color: colors.accent,
    description: 'Professional AC services including installation, repair, and maintenance',
    rating: 4.7,
    totalBookings: '850K',
    images: [
      { id: 'split-ac', url: '❄️', title: 'Split AC service' },
      { id: 'window-ac', url: '🪟', title: 'Window AC service' },
      { id: 'central-ac', url: '🏢', title: 'Central AC service' },
      { id: 'installation', url: '🔧', title: 'AC Installation' },
    ],
    subcategories: [
      {
        id: 'ac-service',
        title: 'AC Service & Repair',
        image: '❄️',
        services: [
          {
            id: 'jet-spray-service',
            name: 'Jet Spray AC Service',
            description: 'Deep cleaning with high-pressure jet spray for optimal cooling performance',
            rating: 4.6,
            reviewCount: '4.2K',
            startingPrice: 649,
            originalPrice: 799,
            duration: '1-2 hours',
            options: 3,
            image: '💨',
            popular: true,
            features: ['Jet spray cleaning', 'Filter replacement', 'Gas check'],
          },
          {
            id: 'foam-jet-service',
            name: 'Foam Jet Split AC Service',
            description: 'Advanced foam cleaning for split ACs with antibacterial treatment',
            rating: 4.5,
            reviewCount: '2.8K',
            startingPrice: 759,
            originalPrice: 899,
            duration: '1.5-2 hours',
            options: 4,
            image: '🫧',
            features: ['Foam cleaning', 'Antibacterial treatment', 'Performance optimization'],
          },
        ],
      },
      {
        id: 'ac-installation',
        title: 'AC Installation',
        image: '🔧',
        services: [
          {
            id: 'split-ac-installation',
            name: 'Split AC Installation',
            description: 'Professional installation of split AC units with warranty',
            rating: 4.7,
            reviewCount: '1.5K',
            startingPrice: 2499,
            duration: '2-3 hours',
            options: 2,
            image: '🏠',
            features: ['Professional installation', '1-year warranty', 'Gas filling included'],
          },
        ],
      },
    ],
    promotions: [
      {
        id: 'ac-summer',
        title: 'Summer Special - 20% off',
        subtitle: 'Beat the heat with our AC services',
        icon: '☀️',
        color: colors.warning,
      },
    ],
  },
  {
    id: 'deep-cleaning',
    name: 'Deep Cleaning',
    icon: '✨',
    color: colors.categories.deepCleaning,
    description: 'Intensive deep cleaning services for thorough sanitization',
    rating: 4.9,
    totalBookings: '1.5M',
    images: [
      { id: 'bathroom-deep', url: '🚿', title: 'Bathroom deep clean' },
      { id: 'kitchen-deep', url: '🍳', title: 'Kitchen deep clean' },
      { id: 'carpet-clean', url: '🧽', title: 'Carpet cleaning' },
      { id: 'sofa-clean', url: '🛋️', title: 'Sofa cleaning' },
    ],
    subcategories: [
      {
        id: 'bathroom-deep-cleaning',
        title: 'Bathroom Deep Cleaning',
        image: '🚿',
        services: [
          {
            id: 'classic-bathroom',
            name: 'Classic Bathroom Cleaning',
            description: 'Thorough bathroom cleaning with descaling and sanitization',
            rating: 4.8,
            reviewCount: '5.1K',
            startingPrice: 419,
            originalPrice: 499,
            duration: '1-1.5 hours',
            options: 2,
            image: '🚿',
            popular: true,
            features: ['Descaling', 'Sanitization', 'Tile cleaning'],
          },
          {
            id: 'premium-bathroom',
            name: 'Premium Bathroom Deep Clean',
            description: 'Complete bathroom transformation with advanced cleaning techniques',
            rating: 4.9,
            reviewCount: '3.8K',
            startingPrice: 799,
            originalPrice: 999,
            duration: '2-2.5 hours',
            options: 3,
            image: '🛁',
            features: ['Advanced descaling', 'Grout cleaning', 'Fixture polishing'],
          },
        ],
      },
    ],
    promotions: [
      {
        id: 'deep-clean-offer',
        title: 'Deep Clean Special',
        subtitle: 'Get 25% off on first booking',
        icon: '🎯',
        color: colors.success,
      },
    ],
  },
];

// Helper function to get category by ID
export const getCategoryById = (categoryId: string): ServiceCategory | undefined => {
  return serviceCategories.find(category => category.id === categoryId);
};

// Helper function to get all services from a category
export const getServicesByCategory = (categoryId: string): ServiceItem[] => {
  const category = getCategoryById(categoryId);
  if (!category) return [];
  
  return category.subcategories.flatMap(subcategory => subcategory.services);
};

// Helper function to get service by ID across all categories
export const getServiceById = (serviceId: string): ServiceItem | undefined => {
  for (const category of serviceCategories) {
    for (const subcategory of category.subcategories) {
      const service = subcategory.services.find(s => s.id === serviceId);
      if (service) return service;
    }
  }
  return undefined;
};
