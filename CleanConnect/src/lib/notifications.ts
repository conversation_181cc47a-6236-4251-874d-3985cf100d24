import { supabase } from './supabase';

export interface Notification {
  id: string;
  type: 'booking_update' | 'message' | 'payment' | 'reminder';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

export interface BookingUpdate {
  id: string;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  progress?: number;
  message?: string;
  timestamp: string;
}

export interface ChatMessage {
  id: string;
  bookingId: string;
  senderId: string;
  senderType: 'customer' | 'provider';
  message: string;
  timestamp: string;
}

class NotificationService {
  private subscriptions: Map<string, any> = new Map();

  // Subscribe to booking updates
  subscribeToBookingUpdates(bookingId: string, callback: (update: BookingUpdate) => void) {
    const subscription = supabase
      .channel(`booking-${bookingId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'bookings',
          filter: `id=eq.${bookingId}`,
        },
        (payload) => {
          const update: BookingUpdate = {
            id: payload.new.id,
            status: payload.new.status,
            progress: payload.new.progress,
            message: payload.new.status_message,
            timestamp: new Date().toISOString(),
          };
          callback(update);
        }
      )
      .subscribe();

    this.subscriptions.set(`booking-${bookingId}`, subscription);
    return subscription;
  }

  // Subscribe to chat messages
  subscribeToChatMessages(bookingId: string, callback: (message: ChatMessage) => void) {
    const subscription = supabase
      .channel(`chat-${bookingId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `booking_id=eq.${bookingId}`,
        },
        (payload) => {
          const message: ChatMessage = {
            id: payload.new.id,
            bookingId: payload.new.booking_id,
            senderId: payload.new.sender_id,
            senderType: payload.new.sender_type,
            message: payload.new.message,
            timestamp: payload.new.created_at,
          };
          callback(message);
        }
      )
      .subscribe();

    this.subscriptions.set(`chat-${bookingId}`, subscription);
    return subscription;
  }

  // Subscribe to user notifications
  subscribeToUserNotifications(userId: string, callback: (notification: Notification) => void) {
    const subscription = supabase
      .channel(`notifications-${userId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          const notification: Notification = {
            id: payload.new.id,
            type: payload.new.type,
            title: payload.new.title,
            message: payload.new.message,
            data: payload.new.data,
            read: payload.new.read,
            createdAt: payload.new.created_at,
          };
          callback(notification);
        }
      )
      .subscribe();

    this.subscriptions.set(`notifications-${userId}`, subscription);
    return subscription;
  }

  // Send a message
  async sendMessage(bookingId: string, senderId: string, senderType: 'customer' | 'provider', message: string) {
    const { data, error } = await supabase
      .from('messages')
      .insert({
        booking_id: bookingId,
        sender_id: senderId,
        sender_type: senderType,
        message: message,
      })
      .select()
      .single();

    if (error) {
      console.error('Error sending message:', error);
      throw error;
    }

    return data;
  }

  // Update booking status
  async updateBookingStatus(bookingId: string, status: string, progress?: number, message?: string) {
    const { data, error } = await supabase
      .from('bookings')
      .update({
        status: status,
        progress: progress,
        status_message: message,
        updated_at: new Date().toISOString(),
      })
      .eq('id', bookingId)
      .select()
      .single();

    if (error) {
      console.error('Error updating booking status:', error);
      throw error;
    }

    return data;
  }

  // Create notification
  async createNotification(userId: string, type: string, title: string, message: string, data?: any) {
    const { data: notification, error } = await supabase
      .from('notifications')
      .insert({
        user_id: userId,
        type: type,
        title: title,
        message: message,
        data: data,
        read: false,
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating notification:', error);
      throw error;
    }

    return notification;
  }

  // Mark notification as read
  async markNotificationAsRead(notificationId: string) {
    const { data, error } = await supabase
      .from('notifications')
      .update({ read: true })
      .eq('id', notificationId)
      .select()
      .single();

    if (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }

    return data;
  }

  // Get user notifications
  async getUserNotifications(userId: string, limit = 50) {
    const { data, error } = await supabase
      .from('notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }

    return data;
  }

  // Get booking messages
  async getBookingMessages(bookingId: string, limit = 100) {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('booking_id', bookingId)
      .order('created_at', { ascending: true })
      .limit(limit);

    if (error) {
      console.error('Error fetching messages:', error);
      throw error;
    }

    return data;
  }

  // Unsubscribe from all subscriptions
  unsubscribeAll() {
    this.subscriptions.forEach((subscription, key) => {
      supabase.removeChannel(subscription);
      console.log(`Unsubscribed from ${key}`);
    });
    this.subscriptions.clear();
  }

  // Unsubscribe from specific subscription
  unsubscribe(channelName: string) {
    const subscription = this.subscriptions.get(channelName);
    if (subscription) {
      supabase.removeChannel(subscription);
      this.subscriptions.delete(channelName);
      console.log(`Unsubscribed from ${channelName}`);
    }
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

// Mock notification service for development (when Supabase is not configured)
export class MockNotificationService {
  private listeners: Map<string, Function[]> = new Map();

  subscribeToBookingUpdates(bookingId: string, callback: (update: BookingUpdate) => void) {
    if (!this.listeners.has(`booking-${bookingId}`)) {
      this.listeners.set(`booking-${bookingId}`, []);
    }
    this.listeners.get(`booking-${bookingId}`)?.push(callback);
    
    // Return mock subscription object
    return {
      unsubscribe: () => {
        const callbacks = this.listeners.get(`booking-${bookingId}`) || [];
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  subscribeToChatMessages(bookingId: string, callback: (message: ChatMessage) => void) {
    if (!this.listeners.has(`chat-${bookingId}`)) {
      this.listeners.set(`chat-${bookingId}`, []);
    }
    this.listeners.get(`chat-${bookingId}`)?.push(callback);
    
    return {
      unsubscribe: () => {
        const callbacks = this.listeners.get(`chat-${bookingId}`) || [];
        const index = callbacks.indexOf(callback);
        if (index > -1) {
          callbacks.splice(index, 1);
        }
      }
    };
  }

  // Simulate booking update
  simulateBookingUpdate(bookingId: string, update: BookingUpdate) {
    const callbacks = this.listeners.get(`booking-${bookingId}`) || [];
    callbacks.forEach(callback => callback(update));
  }

  // Simulate new message
  simulateNewMessage(bookingId: string, message: ChatMessage) {
    const callbacks = this.listeners.get(`chat-${bookingId}`) || [];
    callbacks.forEach(callback => callback(message));
  }

  unsubscribeAll() {
    this.listeners.clear();
  }
}

// Export mock service for development
export const mockNotificationService = new MockNotificationService(); 