import * as Notifications from 'expo-notifications';
import * as Device from 'expo-device';
import Constants from 'expo-constants';
import { Platform } from 'react-native';
import { supabase } from './supabase';

// Check if we're in development mode or Expo Go
const isDevelopment = __DEV__ || Constants.appOwnership === 'expo';
const isExpoGo = Constants.appOwnership === 'expo';

// Configure notification behavior
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export interface PushNotificationData {
  type: 'booking_update' | 'message' | 'payment' | 'reminder' | 'promotion';
  bookingId?: string;
  providerId?: string;
  message?: string;
  title: string;
  body: string;
  data?: any;
  [key: string]: any; // Add index signature for flexibility
}

export interface NotificationSettings {
  bookingUpdates: boolean;
  messages: boolean;
  payments: boolean;
  reminders: boolean;
  promotions: boolean;
}

class PushNotificationService {
  private expoPushToken: string | null = null;
  private notificationListener: Notifications.Subscription | null = null;
  private responseListener: Notifications.Subscription | null = null;

  // Initialize push notifications
  async initialize() {
    try {
      // Skip real push notification setup in Expo Go or development
      if (isExpoGo || isDevelopment) {
        console.log('⚠️  Push notifications disabled in Expo Go/Development mode');
        console.log('📱 Use a development build for full push notification support');
        this.expoPushToken = 'dev-mock-token-' + Date.now();
        return true;
      }

      // Request permissions
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      if (finalStatus !== 'granted') {
        console.log('Failed to get push token for push notification!');
        return false;
      }

      // Get push token only on physical device and production build
      if (Device.isDevice && !isExpoGo) {
        const token = await Notifications.getExpoPushTokenAsync({
          projectId: Constants.expoConfig?.extra?.eas?.projectId,
        });
        this.expoPushToken = token.data;
        console.log('Push token:', this.expoPushToken);
      } else {
        console.log('Must use physical device for Push Notifications');
        this.expoPushToken = 'dev-mock-token-' + Date.now();
      }

      // Set up notification listeners
      this.setupNotificationListeners();

      return true;
    } catch (error) {
      console.error('Error initializing push notifications:', error);
      return false;
    }
  }

  // Set up notification listeners
  private setupNotificationListeners() {
    // Listen for incoming notifications when app is in foreground
    this.notificationListener = Notifications.addNotificationReceivedListener(notification => {
      console.log('Notification received:', notification);
      this.handleNotificationReceived(notification);
    });

    // Listen for notification responses (when user taps notification)
    this.responseListener = Notifications.addNotificationResponseReceivedListener(response => {
      console.log('Notification response:', response);
      this.handleNotificationResponse(response);
    });
  }

  // Handle incoming notifications
  private handleNotificationReceived(notification: Notifications.Notification) {
    const data = notification.request.content.data as unknown as PushNotificationData;
    
    // Update notification store
    // This will be handled by the notification store
    console.log('Handling notification:', data);
  }

  // Handle notification responses (user taps notification)
  private handleNotificationResponse(response: Notifications.NotificationResponse) {
    const data = response.notification.request.content.data as unknown as PushNotificationData;
    
    // Navigate based on notification type
    this.navigateFromNotification(data);
  }

  // Navigate to appropriate screen based on notification
  private navigateFromNotification(data: PushNotificationData) {
    // Import router dynamically to avoid circular dependencies
    const { router } = require('expo-router');
    
    switch (data.type) {
      case 'booking_update':
        if (data.bookingId) {
          router.push('/customer/bookings');
        }
        break;
      case 'message':
        if (data.bookingId) {
          router.push('/customer/chat');
        }
        break;
      case 'payment':
        // Navigate to payment screen
        console.log('Navigate to payment screen');
        break;
      case 'reminder':
        // Navigate to bookings screen
        router.push('/customer/bookings');
        break;
      case 'promotion':
        // Navigate to deals/promotions screen
        console.log('Navigate to promotions screen');
        break;
      default:
        console.log('Unknown notification type:', data.type);
    }
  }

  // Register device token with backend
  async registerDeviceToken(userId: string) {
    if (!this.expoPushToken) {
      console.log('No push token available');
      return false;
    }

    // Skip database operations in development mode
    if (isDevelopment || isExpoGo) {
      console.log('📱 Mock: Device token registered for user:', userId);
      console.log('🔧 Token:', this.expoPushToken);
      return true;
    }

    try {
      const { data, error } = await supabase
        .from('user_devices')
        .upsert({
          user_id: userId,
          device_token: this.expoPushToken,
          platform: Platform.OS,
          device_model: Device.modelName || 'Unknown',
          app_version: Constants.expoConfig?.version || '1.0.0',
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error registering device token:', error);
        return false;
      }

      console.log('Device token registered successfully');
      return true;
    } catch (error) {
      console.error('Error registering device token:', error);
      return false;
    }
  }

  // Unregister device token
  async unregisterDeviceToken(userId: string) {
    try {
      const { error } = await supabase
        .from('user_devices')
        .delete()
        .eq('user_id', userId)
        .eq('device_token', this.expoPushToken);

      if (error) {
        console.error('Error unregistering device token:', error);
        return false;
      }

      console.log('Device token unregistered successfully');
      return true;
    } catch (error) {
      console.error('Error unregistering device token:', error);
      return false;
    }
  }

  // Update notification settings
  async updateNotificationSettings(userId: string, settings: NotificationSettings) {
    // Skip database operations in development mode
    if (isDevelopment || isExpoGo) {
      console.log('📱 Mock: Notification settings updated for user:', userId);
      console.log('⚙️  Settings:', settings);
      return true;
    }

    try {
      const { data, error } = await supabase
        .from('user_notification_settings')
        .upsert({
          user_id: userId,
          booking_updates: settings.bookingUpdates,
          messages: settings.messages,
          payments: settings.payments,
          reminders: settings.reminders,
          promotions: settings.promotions,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (error) {
        console.error('Error updating notification settings:', error);
        return false;
      }

      console.log('Notification settings updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating notification settings:', error);
      return false;
    }
  }

  // Get notification settings
  async getNotificationSettings(userId: string): Promise<NotificationSettings | null> {
    // Return mock settings in development mode
    if (isDevelopment || isExpoGo) {
      console.log('📱 Mock: Fetching notification settings for user:', userId);
      return {
        bookingUpdates: true,
        messages: true,
        payments: true,
        reminders: true,
        promotions: false,
      };
    }

    try {
      const { data, error } = await supabase
        .from('user_notification_settings')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (error) {
        console.error('Error fetching notification settings:', error);
        return null;
      }

      return {
        bookingUpdates: data.booking_updates ?? true,
        messages: data.messages ?? true,
        payments: data.payments ?? true,
        reminders: data.reminders ?? true,
        promotions: data.promotions ?? false,
      };
    } catch (error) {
      console.error('Error fetching notification settings:', error);
      return null;
    }
  }

  // Schedule local notification
  async scheduleLocalNotification(
    title: string,
    body: string,
    data: PushNotificationData,
    trigger?: Notifications.NotificationTriggerInput
  ) {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data as Record<string, unknown>,
          sound: 'default',
        },
        trigger: trigger || null,
      });

      console.log('Local notification scheduled:', notificationId);
      return notificationId;
    } catch (error) {
      console.error('Error scheduling local notification:', error);
      return null;
    }
  }

  // Cancel scheduled notification
  async cancelScheduledNotification(notificationId: string) {
    try {
      await Notifications.cancelScheduledNotificationAsync(notificationId);
      console.log('Scheduled notification cancelled:', notificationId);
      return true;
    } catch (error) {
      console.error('Error cancelling scheduled notification:', error);
      return false;
    }
  }

  // Cancel all scheduled notifications
  async cancelAllScheduledNotifications() {
    try {
      await Notifications.cancelAllScheduledNotificationsAsync();
      console.log('All scheduled notifications cancelled');
      return true;
    } catch (error) {
      console.error('Error cancelling all scheduled notifications:', error);
      return false;
    }
  }

  // Get badge count
  async getBadgeCount(): Promise<number> {
    try {
      return await Notifications.getBadgeCountAsync();
    } catch (error) {
      console.error('Error getting badge count:', error);
      return 0;
    }
  }

  // Set badge count
  async setBadgeCount(count: number) {
    try {
      await Notifications.setBadgeCountAsync(count);
      console.log('Badge count set to:', count);
      return true;
    } catch (error) {
      console.error('Error setting badge count:', error);
      return false;
    }
  }

  // Clear badge count
  async clearBadgeCount() {
    try {
      await Notifications.setBadgeCountAsync(0);
      console.log('Badge count cleared');
      return true;
    } catch (error) {
      console.error('Error clearing badge count:', error);
      return false;
    }
  }

  // Get push token
  getPushToken(): string | null {
    return this.expoPushToken;
  }

  // Check if notifications are enabled
  async areNotificationsEnabled(): Promise<boolean> {
    try {
      const { status } = await Notifications.getPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error checking notification permissions:', error);
      return false;
    }
  }

  // Request notification permissions
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await Notifications.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return false;
    }
  }

  // Clean up listeners
  cleanup() {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }
}

// Create singleton instance
export const pushNotificationService = new PushNotificationService();

// Mock push notification service for development
export class MockPushNotificationService {
  private expoPushToken: string | null = 'mock-push-token-123';
  private notificationSettings: NotificationSettings = {
    bookingUpdates: true,
    messages: true,
    payments: true,
    reminders: true,
    promotions: false,
  };

  async initialize() {
    console.log('Mock push notification service initialized');
    return true;
  }

  async registerDeviceToken(userId: string) {
    console.log('Mock: Device token registered for user:', userId);
    return true;
  }

  async unregisterDeviceToken(userId: string) {
    console.log('Mock: Device token unregistered for user:', userId);
    return true;
  }

  async updateNotificationSettings(userId: string, settings: NotificationSettings) {
    this.notificationSettings = settings;
    console.log('Mock: Notification settings updated:', settings);
    return true;
  }

  async getNotificationSettings(userId: string): Promise<NotificationSettings> {
    return this.notificationSettings;
  }

  async scheduleLocalNotification(
    title: string,
    body: string,
    data: PushNotificationData,
    trigger?: any
  ) {
    console.log('Mock: Local notification scheduled:', { title, body, data });
    return 'mock-notification-id';
  }

  async cancelScheduledNotification(notificationId: string) {
    console.log('Mock: Scheduled notification cancelled:', notificationId);
    return true;
  }

  async cancelAllScheduledNotifications() {
    console.log('Mock: All scheduled notifications cancelled');
    return true;
  }

  async getBadgeCount(): Promise<number> {
    return 0;
  }

  async setBadgeCount(count: number) {
    console.log('Mock: Badge count set to:', count);
    return true;
  }

  async clearBadgeCount() {
    console.log('Mock: Badge count cleared');
    return true;
  }

  getPushToken(): string | null {
    return this.expoPushToken;
  }

  async areNotificationsEnabled(): Promise<boolean> {
    return true;
  }

  async requestPermissions(): Promise<boolean> {
    return true;
  }

  cleanup() {
    console.log('Mock: Push notification service cleaned up');
  }
}

// Export mock service for development
export const mockPushNotificationService = new MockPushNotificationService();

// Auto-select the appropriate service based on environment
export const notificationService = isDevelopment || isExpoGo
  ? mockPushNotificationService
  : pushNotificationService;

// Development helper to show current mode
if (isDevelopment) {
  console.log('🔧 Push Notifications: Using Mock Service (Development Mode)');
  if (isExpoGo) {
    console.log('📱 Expo Go detected - Push notifications are limited');
    console.log('💡 Use a development build for full push notification support');
  }
}