import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import 'react-native-url-polyfill/auto';

// Environment configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co';
const supabaseAnonKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key';
const isDevelopment = __DEV__ || process.env.EXPO_PUBLIC_DEV_MODE === 'true';

// Check if we have valid Supabase configuration
const hasValidConfig = supabaseUrl !== 'https://placeholder.supabase.co' &&
                      supabaseAnonKey !== 'placeholder-key';

if (!hasValidConfig && !isDevelopment) {
  console.warn('⚠️  Supabase configuration missing. Please check your environment variables.');
}

export const supabase = createClient(supabaseUrl, supabase<PERSON>nonKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: hasValidConfig,
    persistSession: hasValidConfig,
    detectSessionInUrl: false,
  },
  global: {
    headers: {
      'X-Client-Info': 'cleanconnect-mobile',
    },
  },
});

// Auth helper functions with error handling
export const auth = {
  signInWithPhone: async (phone: string) => {
    try {
      if (!hasValidConfig) {
        console.log('📱 Mock: Sign in with phone:', phone);
        return {
          data: { user: null, session: null },
          error: null
        };
      }
      const { data, error } = await supabase.auth.signInWithOtp({
        phone,
      });
      return { data, error };
    } catch (error) {
      console.error('Network error in signInWithPhone:', error);
      return { data: null, error: { message: 'Network error. Please check your connection.' } };
    }
  },

  verifyOtp: async (phone: string, token: string) => {
    try {
      if (!hasValidConfig) {
        console.log('📱 Mock: Verify OTP for phone:', phone);
        return {
          data: { user: { id: 'mock-user', phone }, session: { access_token: 'mock-token' } },
          error: null
        };
      }
      const { data, error } = await supabase.auth.verifyOtp({
        phone,
        token,
        type: 'sms',
      });
      return { data, error };
    } catch (error) {
      console.error('Network error in verifyOtp:', error);
      return { data: null, error: { message: 'Network error. Please check your connection.' } };
    }
  },

  signInWithEmail: async (email: string, password: string) => {
    try {
      if (!hasValidConfig) {
        console.log('📱 Mock: Sign in with email:', email);
        return {
          data: { user: { id: 'mock-user', email }, session: { access_token: 'mock-token' } },
          error: null
        };
      }
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });
      return { data, error };
    } catch (error) {
      console.error('Network error in signInWithEmail:', error);
      return { data: null, error: { message: 'Network error. Please check your connection.' } };
    }
  },

  signUp: async (email: string, password: string) => {
    try {
      if (!hasValidConfig) {
        console.log('📱 Mock: Sign up with email:', email);
        return {
          data: { user: { id: 'mock-user', email }, session: null },
          error: null
        };
      }
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
      });
      return { data, error };
    } catch (error) {
      console.error('Network error in signUp:', error);
      return { data: null, error: { message: 'Network error. Please check your connection.' } };
    }
  },

  signOut: async () => {
    try {
      if (!hasValidConfig) {
        console.log('📱 Mock: Sign out');
        return { error: null };
      }
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      console.error('Network error in signOut:', error);
      return { error: { message: 'Network error. Please check your connection.' } };
    }
  },

  getCurrentUser: async () => {
    try {
      if (!hasValidConfig) {
        return { data: { user: null }, error: null };
      }
      return await supabase.auth.getUser();
    } catch (error) {
      console.error('Network error in getCurrentUser:', error);
      return { data: { user: null }, error: { message: 'Network error. Please check your connection.' } };
    }
  },

  onAuthStateChange: (callback: (event: string, session: any) => void) => {
    if (!hasValidConfig) {
      console.log('📱 Mock: Auth state change listener registered');
      return { data: { subscription: { unsubscribe: () => {} } } };
    }
    return supabase.auth.onAuthStateChange(callback);
  },
};