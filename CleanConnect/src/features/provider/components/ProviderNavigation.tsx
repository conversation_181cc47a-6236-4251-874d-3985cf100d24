import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { router, usePathname } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { useProviderStore } from '../../../store/providerStore';

interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  route: string;
  showBadge?: boolean;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: '🏠',
    route: '/provider/dashboard',
  },
  {
    id: 'bookings',
    label: 'Booking',
    icon: '📋',
    route: '/provider/bookings',
    showBadge: true,
  },
  {
    id: 'earnings',
    label: 'Earning',
    icon: '💰',
    route: '/provider/earnings',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: '👤',
    route: '/provider/profile',
  },
];

export const ProviderNavigation: React.FC = () => {
  const pathname = usePathname();
  const { getPendingRequestsCount, getUnreadNotificationsCount } = useProviderStore();

  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  const isActiveRoute = (route: string): boolean => {
    // Handle exact matches
    if (pathname === route) return true;

    // Handle nested routes
    if (route === '/provider/dashboard' && (
        pathname === '/provider/dashboard' ||
        pathname.startsWith('/provider/booking-details') ||
        (pathname.startsWith('/provider') &&
         !pathname.includes('/bookings') &&
         !pathname.includes('/earnings') &&
         !pathname.includes('/profile'))
    )) {
      return true;
    }

    if (route === '/provider/bookings' && pathname.includes('/provider/bookings')) {
      return true;
    }

    if (route === '/provider/earnings' && pathname.includes('/provider/earnings')) {
      return true;
    }

    if (route === '/provider/profile' && pathname.includes('/provider/profile')) {
      return true;
    }

    return false;
  };

  const getBadgeCount = (itemId: string): number => {
    switch (itemId) {
      case 'bookings':
        return getPendingRequestsCount();
      default:
        return 0;
    }
  };

  return (
    <View style={styles.container}>
      {navigationItems.map((item) => {
        const isActive = isActiveRoute(item.route);
        const badgeCount = getBadgeCount(item.id);
        const showBadge = item.showBadge && badgeCount > 0;

        return (
          <TouchableOpacity
            key={item.id}
            style={styles.navItem}
            onPress={() => handleNavigation(item.route)}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              <Text style={[
                styles.navIcon,
                isActive && styles.navIconActive,
              ]}>
                {item.icon}
              </Text>
              {showBadge && (
                <View style={styles.badge}>
                  <Text style={styles.badgeText}>
                    {badgeCount > 99 ? '99+' : badgeCount}
                  </Text>
                </View>
              )}
            </View>
            <Text style={[
              styles.navLabel,
              isActive && styles.navLabelActive,
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
  } as ViewStyle,
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.xs,
  } as ViewStyle,
  iconContainer: {
    position: 'relative',
    marginBottom: spacing.xs,
  } as ViewStyle,
  navIcon: {
    fontSize: 24,
    textAlign: 'center',
  } as TextStyle,
  navIconActive: {
    transform: [{ scale: 1.1 }],
  } as TextStyle,
  navLabel: {
    fontSize: typography.sizes.xs,
    color: colors.gray[600],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,
  navLabelActive: {
    color: colors.primary,
    fontWeight: typography.weights.semibold,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
  badge: {
    position: 'absolute',
    top: -4,
    right: -8,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs,
  } as ViewStyle,
  badgeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.bold,
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,
});