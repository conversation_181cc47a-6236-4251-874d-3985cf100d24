import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { useProviderStore } from '../../../store/providerStore';

const { width } = Dimensions.get('window');

export const ProviderBookingDetailsScreen: React.FC = () => {
  const { id } = useLocalSearchParams();
  const { serviceRequests, acceptServiceRequest, declineServiceRequest } = useProviderStore();
  
  // Find the specific booking request
  const booking = serviceRequests.find(request => request.id === id);

  if (!booking) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Booking not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  const handleAccept = () => {
    acceptServiceRequest(booking.id);
    router.back();
  };

  const handleDecline = () => {
    declineServiceRequest(booking.id);
    router.back();
  };

  const handleRequestExtraTime = () => {
    // Handle request extra time functionality
    console.log('Request extra time');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Booking Details</Text>
        <TouchableOpacity style={styles.extraTimeButton} onPress={handleRequestExtraTime}>
          <Text style={styles.extraTimeText}>Request Extra Time</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Service Image */}
        <View style={styles.imageContainer}>
          <Image
            source={{ 
              uri: booking.image || 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=400&h=300&fit=crop'
            }}
            style={styles.serviceImage}
          />
        </View>

        {/* Service Info */}
        <View style={styles.serviceInfo}>
          <Text style={styles.serviceTitle}>
            {booking.service}: {booking.serviceType} Services Doorstep
          </Text>
          <Text style={styles.servicePrice}>
            Start from ${booking.price} <Text style={styles.servicePriceNote}>each</Text>
          </Text>
        </View>

        {/* Client Details */}
        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>👤</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Client Name</Text>
              <Text style={styles.detailValue}>{booking.customerName}</Text>
            </View>
            <TouchableOpacity style={styles.contactButton}>
              <Text style={styles.contactIcon}>💬</Text>
            </TouchableOpacity>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>🏷️</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Service Type</Text>
              <Text style={styles.detailValue}>{booking.serviceType}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>📅</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Date & Time</Text>
              <Text style={styles.detailValue}>{booking.date}, {booking.time}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>⏱️</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Booking Time</Text>
              <Text style={styles.detailValue}>{booking.duration}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>📍</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Location</Text>
              <Text style={styles.detailValue}>{booking.address}</Text>
            </View>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.detailIcon}>
              <Text style={styles.iconText}>🔧</Text>
            </View>
            <View style={styles.detailContent}>
              <Text style={styles.detailLabel}>Required services</Text>
              <Text style={styles.detailValue}>
                {booking.description || 'Standard cleaning service'}
              </Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Action Buttons */}
      <View style={styles.actionContainer}>
        <TouchableOpacity style={styles.declineButton} onPress={handleDecline}>
          <Text style={styles.declineButtonText}>Decline</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.acceptButton} onPress={handleAccept}>
          <Text style={styles.acceptButtonText}>Accept</Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 20,
    color: colors.text.primary,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
  },
  extraTimeButton: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
  },
  extraTimeText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  content: {
    flex: 1,
  },
  imageContainer: {
    width: '100%',
    height: 250,
    backgroundColor: colors.gray[100],
  },
  serviceImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  serviceInfo: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  serviceTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.lg * typography.lineHeights.normal,
  },
  servicePrice: {
    fontSize: typography.sizes.md,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.medium,
  },
  servicePriceNote: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
  },
  detailsContainer: {
    backgroundColor: colors.white,
    marginTop: spacing.sm,
    paddingVertical: spacing.md,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  detailIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  iconText: {
    fontSize: 18,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
    marginBottom: spacing.xs,
  },
  detailValue: {
    fontSize: typography.sizes.md,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.medium,
  },
  contactButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
  },
  contactIcon: {
    fontSize: 18,
  },
  actionContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    backgroundColor: colors.white,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    gap: spacing.md,
  },
  declineButton: {
    flex: 1,
    paddingVertical: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.gray[300],
    alignItems: 'center',
    justifyContent: 'center',
  },
  declineButtonText: {
    fontSize: typography.sizes.md,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.semibold,
  },
  acceptButton: {
    flex: 1,
    paddingVertical: spacing.lg,
    borderRadius: 12,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  acceptButtonText: {
    fontSize: typography.sizes.md,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  errorText: {
    fontSize: typography.sizes.lg,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
  },
});
