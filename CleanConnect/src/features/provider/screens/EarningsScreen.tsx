import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { ProviderNavigation } from '../components/ProviderNavigation';

// Mock earnings data
const earningsData = {
  totalEarnings: 1250,
  thisMonth: 320,
  lastMonth: 280,
  thisWeek: 85,
  pendingPayout: 45,
  completedJobs: 45,
  averagePerJob: 27.8,
};

const monthlyEarnings = [
  { month: 'Jan', earnings: 180 },
  { month: 'Feb', earnings: 220 },
  { month: 'Mar', earnings: 195 },
  { month: 'Apr', earnings: 280 },
  { month: 'May', earnings: 320 },
  { month: 'Jun', earnings: 320 },
];

const recentTransactions = [
  {
    id: '1',
    customerName: '<PERSON>',
    service: 'House Cleaning',
    amount: 50,
    date: 'Today',
    status: 'COMPLETED',
    jobId: 'JOB-001',
  },
  {
    id: '2',
    customerName: '<PERSON>',
    service: 'Deep Cleaning',
    amount: 80,
    date: 'Yesterday',
    status: 'COMPLETED',
    jobId: 'JOB-002',
  },
  {
    id: '3',
    customerName: 'Fatou Diallo',
    service: 'Kitchen Cleaning',
    amount: 40,
    date: '2 days ago',
    status: 'PENDING',
    jobId: 'JOB-003',
  },
  {
    id: '4',
    customerName: 'Ousmane Jallow',
    service: 'Laundry Service',
    amount: 25,
    date: '3 days ago',
    status: 'COMPLETED',
    jobId: 'JOB-004',
  },
  {
    id: '5',
    customerName: 'Aminata Ceesay',
    service: 'Window Cleaning',
    amount: 30,
    date: '1 week ago',
    status: 'COMPLETED',
    jobId: 'JOB-005',
  },
];

const timeFilters = [
  { id: 'week', label: 'This Week' },
  { id: 'month', label: 'This Month' },
  { id: 'quarter', label: 'This Quarter' },
  { id: 'year', label: 'This Year' },
];

export const EarningsScreen: React.FC = () => {
  const [selectedTimeFilter, setSelectedTimeFilter] = useState('month');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return colors.success;
      case 'PENDING':
        return colors.warning;
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Paid';
      case 'PENDING':
        return 'Pending';
      default:
        return status;
    }
  };

  const handleWithdrawFunds = () => {
    console.log('Withdraw funds');
    // Implement withdrawal functionality
  };

  const handleViewAllTransactions = () => {
    console.log('View all transactions');
    // Navigate to full transaction history
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Earnings</Text>
        <Text style={styles.headerSubtitle}>Track your income and payments</Text>
      </View>

      {/* Main Earnings Overview */}
      <View style={styles.earningsOverview}>
        <View style={styles.mainEarningsCard}>
          <Text style={styles.mainEarningsLabel}>Total Earnings</Text>
          <Text style={styles.mainEarningsAmount}>${earningsData.totalEarnings}</Text>
          <Text style={styles.mainEarningsSubtext}>Lifetime earnings</Text>
        </View>
      </View>

      {/* Quick Stats */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>${earningsData.thisMonth}</Text>
          <Text style={styles.statLabel}>This Month</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>${earningsData.thisWeek}</Text>
          <Text style={styles.statLabel}>This Week</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>${earningsData.pendingPayout}</Text>
          <Text style={styles.statLabel}>Pending</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{earningsData.completedJobs}</Text>
          <Text style={styles.statLabel}>Jobs Done</Text>
        </View>
      </View>

      {/* Withdraw Section */}
      <View style={styles.withdrawSection}>
        <View style={styles.withdrawCard}>
          <View style={styles.withdrawInfo}>
            <Text style={styles.withdrawLabel}>Available for Withdrawal</Text>
            <Text style={styles.withdrawAmount}>${earningsData.pendingPayout}</Text>
          </View>
          <TouchableOpacity
            style={styles.withdrawButton}
            onPress={handleWithdrawFunds}
          >
            <Text style={styles.withdrawButtonText}>Withdraw</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Performance Metrics */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Performance</Text>
        <View style={styles.metricsCard}>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Average per Job</Text>
            <Text style={styles.metricValue}>${earningsData.averagePerJob}</Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Jobs Completed</Text>
            <Text style={styles.metricValue}>{earningsData.completedJobs}</Text>
          </View>
          <View style={styles.metricRow}>
            <Text style={styles.metricLabel}>Success Rate</Text>
            <Text style={styles.metricValue}>98%</Text>
          </View>
        </View>
      </View>

      {/* Monthly Chart */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Monthly Earnings</Text>
        <View style={styles.chartContainer}>
          <View style={styles.chartBars}>
            {monthlyEarnings.map((item, index) => (
              <View key={index} style={styles.chartBarContainer}>
                <View
                  style={[
                    styles.chartBar,
                    {
                      height: (item.earnings / 320) * 100,
                      backgroundColor: index === monthlyEarnings.length - 1 ? colors.primary : colors.gray[300],
                    },
                  ]}
                />
                <Text style={styles.chartLabel}>{item.month}</Text>
              </View>
            ))}
          </View>
        </View>
      </View>

      {/* Recent Transactions */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Transactions</Text>
          <TouchableOpacity onPress={handleViewAllTransactions}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        <View style={styles.transactionsContainer}>
          {recentTransactions.map((transaction) => (
            <View key={transaction.id} style={styles.transactionCard}>
              <View style={styles.transactionInfo}>
                <Text style={styles.customerName}>{transaction.customerName}</Text>
                <Text style={styles.serviceName}>{transaction.service}</Text>
                <Text style={styles.jobId}>{transaction.jobId}</Text>
                <Text style={styles.transactionDate}>{transaction.date}</Text>
              </View>
              <View style={styles.transactionAmount}>
                <Text style={styles.amountText}>${transaction.amount}</Text>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(transaction.status) }
                ]}>
                  <Text style={styles.statusText}>
                    {getStatusText(transaction.status)}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </View>
      </View>

      {/* Bottom Spacing */}
      <View style={styles.bottomSpacing} />
      
      {/* Navigation */}
      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
  },
  earningsOverview: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  mainEarningsCard: {
    backgroundColor: colors.primary,
    borderRadius: 16,
    padding: spacing.xl,
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 5,
  },
  mainEarningsLabel: {
    fontSize: typography.sizes.md,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.sm,
  },
  mainEarningsAmount: {
    fontSize: typography.sizes.xxxl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    marginBottom: spacing.xs,
  },
  mainEarningsSubtext: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    opacity: 0.8,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  statCard: {
    width: '48%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    marginHorizontal: '1%',
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    textAlign: 'center',
  },
  withdrawSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  withdrawCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  withdrawInfo: {
    flex: 1,
  },
  withdrawLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    marginBottom: spacing.xs,
  },
  withdrawAmount: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.success,
  },
  withdrawButton: {
    backgroundColor: colors.success,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },
  withdrawButtonText: {
    color: colors.white,
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
  },
  section: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  viewAllText: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  metricsCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  metricRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  metricLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
  },
  metricValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  chartContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  chartBars: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 120,
    paddingBottom: spacing.md,
  },
  chartBarContainer: {
    flex: 1,
    alignItems: 'center',
  },
  chartBar: {
    width: 20,
    borderRadius: 10,
    marginBottom: spacing.sm,
  },
  chartLabel: {
    fontSize: typography.sizes.xs,
    color: colors.gray[600],
  },
  transactionsContainer: {
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  transactionCard: {
    flexDirection: 'row',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  transactionInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  serviceName: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  jobId: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    marginBottom: spacing.xs,
  },
  transactionDate: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
  },
  transactionAmount: {
    alignItems: 'flex-end',
  },
  amountText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    color: colors.white,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
}); 