import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { Button } from '../../../components/common/Button';
import { ProviderNavigation } from '../components/ProviderNavigation';

// Mock bookings data
const allBookings = [
  {
    id: '1',
    customerName: '<PERSON>',
    customerPhone: '+************',
    service: 'House Cleaning',
    date: 'Today',
    time: '10:00 AM',
    status: 'PENDING',
    amount: 50,
    address: '123 Main St, Banjul',
    duration: '2-3 hours',
    specialInstructions: 'Please focus on the kitchen area',
  },
  {
    id: '2',
    customerName: '<PERSON>',
    customerPhone: '+************',
    service: 'Deep Cleaning',
    date: 'Tomorrow',
    time: '02:00 PM',
    status: 'CONFIRMED',
    amount: 80,
    address: '456 Business Ave, Banjul',
    duration: '4-5 hours',
    specialInstructions: 'Moving out cleaning',
  },
  {
    id: '3',
    customerName: 'Fatou Diallo',
    customerPhone: '+************',
    service: 'Kitchen Cleaning',
    date: 'Yesterday',
    time: '11:00 AM',
    status: 'COMPLETED',
    amount: 40,
    address: '789 Residential Rd, Banjul',
    duration: '2 hours',
    specialInstructions: 'Regular cleaning',
  },
  {
    id: '4',
    customerName: 'Ousmane Jallow',
    customerPhone: '+************',
    service: 'Laundry Service',
    date: 'Today',
    time: '03:00 PM',
    status: 'IN_PROGRESS',
    amount: 25,
    address: '321 Beach Rd, Banjul',
    duration: '24 hours',
    specialInstructions: 'Delicate items included',
  },
  {
    id: '5',
    customerName: 'Aminata Ceesay',
    customerPhone: '+************',
    service: 'Window Cleaning',
    date: 'Tomorrow',
    time: '09:00 AM',
    status: 'PENDING',
    amount: 30,
    address: '654 Garden St, Banjul',
    duration: '1 hour',
    specialInstructions: 'Second floor windows only',
  },
];

const statusFilters = [
  { id: 'all', label: 'All', count: allBookings.length },
  { id: 'pending', label: 'Pending', count: allBookings.filter(b => b.status === 'PENDING').length },
  { id: 'confirmed', label: 'Confirmed', count: allBookings.filter(b => b.status === 'CONFIRMED').length },
  { id: 'in_progress', label: 'In Progress', count: allBookings.filter(b => b.status === 'IN_PROGRESS').length },
  { id: 'completed', label: 'Completed', count: allBookings.filter(b => b.status === 'COMPLETED').length },
];

export const BookingManagementScreen: React.FC = () => {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [selectedBooking, setSelectedBooking] = useState<string | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return colors.success;
      case 'IN_PROGRESS':
        return colors.warning;
      case 'CONFIRMED':
        return colors.primary;
      case 'PENDING':
        return colors.gray[500];
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Completed';
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'CONFIRMED':
        return 'Confirmed';
      case 'PENDING':
        return 'Pending';
      default:
        return status;
    }
  };

  const getFilteredBookings = () => {
    if (selectedFilter === 'all') {
      return allBookings;
    }
    const statusMap: { [key: string]: string } = {
      pending: 'PENDING',
      confirmed: 'CONFIRMED',
      in_progress: 'IN_PROGRESS',
      completed: 'COMPLETED',
    };
    return allBookings.filter(booking => booking.status === statusMap[selectedFilter]);
  };

  const handleAcceptBooking = (bookingId: string) => {
    Alert.alert(
      'Accept Booking',
      'Are you sure you want to accept this booking?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          onPress: () => {
            console.log('Booking accepted:', bookingId);
            // Update booking status in backend
          },
        },
      ]
    );
  };

  const handleRejectBooking = (bookingId: string) => {
    Alert.alert(
      'Reject Booking',
      'Are you sure you want to reject this booking?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reject',
          style: 'destructive',
          onPress: () => {
            console.log('Booking rejected:', bookingId);
            // Update booking status in backend
          },
        },
      ]
    );
  };

  const handleStartJob = (bookingId: string) => {
    Alert.alert(
      'Start Job',
      'Are you ready to start this job?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Start',
          onPress: () => {
            console.log('Job started:', bookingId);
            // Update booking status in backend
          },
        },
      ]
    );
  };

  const handleCompleteJob = (bookingId: string) => {
    Alert.alert(
      'Complete Job',
      'Are you sure you want to mark this job as completed?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Complete',
          onPress: () => {
            console.log('Job completed:', bookingId);
            // Update booking status in backend
          },
        },
      ]
    );
  };

  const handleCallCustomer = (phone: string) => {
    console.log('Calling customer:', phone);
    // Implement phone call functionality
  };

  const handleBookingPress = (booking: typeof allBookings[0]) => {
    setSelectedBooking(selectedBooking === booking.id ? null : booking.id);
  };

  const renderActionButtons = (booking: typeof allBookings[0]) => {
    switch (booking.status) {
      case 'PENDING':
        return (
          <View style={styles.actionButtons}>
            <Button
              title="Accept"
              onPress={() => handleAcceptBooking(booking.id)}
              style={[styles.actionButton, styles.acceptButton]}
              size="small"
            />
            <Button
              title="Reject"
              onPress={() => handleRejectBooking(booking.id)}
              style={[styles.actionButton, styles.rejectButton]}
              size="small"
            />
          </View>
        );
      case 'CONFIRMED':
        return (
          <View style={styles.actionButtons}>
            <Button
              title="Start Job"
              onPress={() => handleStartJob(booking.id)}
              style={[styles.actionButton, styles.startButton]}
              size="small"
            />
          </View>
        );
      case 'IN_PROGRESS':
        return (
          <View style={styles.actionButtons}>
            <Button
              title="Complete"
              onPress={() => handleCompleteJob(booking.id)}
              style={[styles.actionButton, styles.completeButton]}
              size="small"
            />
          </View>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>My Bookings</Text>
        <Text style={styles.headerSubtitle}>Manage your service requests</Text>
      </View>

      {/* Status Filters */}
      <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.filtersContainer}>
        {statusFilters.map((filter) => (
          <TouchableOpacity
            key={filter.id}
            style={[
              styles.filterChip,
              selectedFilter === filter.id && styles.filterChipSelected,
            ]}
            onPress={() => setSelectedFilter(filter.id)}
          >
            <Text style={[
              styles.filterText,
              selectedFilter === filter.id && styles.filterTextSelected,
            ]}>
              {filter.label} ({filter.count})
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>

      {/* Bookings List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {getFilteredBookings().map((booking) => (
          <View key={booking.id} style={styles.bookingCard}>
            <TouchableOpacity
              style={styles.bookingHeader}
              onPress={() => handleBookingPress(booking)}
            >
              <View style={styles.bookingInfo}>
                <Text style={styles.customerName}>{booking.customerName}</Text>
                <Text style={styles.serviceName}>{booking.service}</Text>
                <View style={styles.bookingMeta}>
                  <Text style={styles.bookingMetaText}>📅 {booking.date}</Text>
                  <Text style={styles.bookingMetaText}>⏰ {booking.time}</Text>
                  <Text style={styles.bookingMetaText}>⏱️ {booking.duration}</Text>
                </View>
              </View>
              <View style={styles.bookingStatus}>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(booking.status) }
                ]}>
                  <Text style={styles.statusText}>
                    {getStatusText(booking.status)}
                  </Text>
                </View>
                <Text style={styles.bookingAmount}>${booking.amount}</Text>
              </View>
            </TouchableOpacity>

            {/* Expanded Details */}
            {selectedBooking === booking.id && (
              <View style={styles.bookingDetails}>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Address:</Text>
                  <Text style={styles.detailValue}>{booking.address}</Text>
                </View>
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Phone:</Text>
                  <TouchableOpacity onPress={() => handleCallCustomer(booking.customerPhone)}>
                    <Text style={styles.phoneNumber}>{booking.customerPhone}</Text>
                  </TouchableOpacity>
                </View>
                {booking.specialInstructions && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Special Instructions:</Text>
                    <Text style={styles.detailValue}>{booking.specialInstructions}</Text>
                  </View>
                )}
                
                {/* Action Buttons */}
                {renderActionButtons(booking)}
              </View>
            )}
          </View>
        ))}
      </ScrollView>

      {/* Bottom Spacing */}
      <View style={styles.bottomSpacing} />
      
      {/* Navigation */}
      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
  },
  filtersContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  filterChip: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  filterChipSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  filterText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontWeight: typography.weights.medium,
  },
  filterTextSelected: {
    color: colors.white,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  bookingCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingHeader: {
    flexDirection: 'row',
    padding: spacing.lg,
  },
  bookingInfo: {
    flex: 1,
  },
  customerName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  serviceName: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    marginBottom: spacing.sm,
  },
  bookingMeta: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  bookingMetaText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    marginRight: spacing.md,
  },
  bookingStatus: {
    alignItems: 'flex-end',
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    marginBottom: spacing.sm,
  },
  statusText: {
    color: colors.white,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  bookingAmount: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  bookingDetails: {
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    padding: spacing.lg,
  },
  detailRow: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  detailLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontWeight: typography.weights.medium,
    width: 120,
  },
  detailValue: {
    fontSize: typography.sizes.sm,
    color: colors.gray[900],
    flex: 1,
  },
  phoneNumber: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    textDecorationLine: 'underline',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: spacing.md,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: spacing.xs,
  },
  acceptButton: {
    backgroundColor: colors.success,
  },
  rejectButton: {
    backgroundColor: colors.error,
  },
  startButton: {
    backgroundColor: colors.warning,
  },
  completeButton: {
    backgroundColor: colors.success,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
}); 