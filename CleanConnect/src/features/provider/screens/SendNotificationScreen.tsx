import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  SafeAreaView,
  TextInput,
  Alert,
  TouchableOpacity,
} from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { Button } from '../../../components/common/Button';
import { useNotificationStore } from '../../../store/useNotificationStore';

// Mock customers/bookings for demo
const mockCustomers = [
  { id: 'user-123', name: '<PERSON><PERSON>', phone: '+************' },
  { id: 'user-456', name: '<PERSON><PERSON>', phone: '+************' },
];
const mockBookings = [
  { id: 'booking-1', customerId: 'user-123', service: 'House Cleaning', date: '2024-01-20', status: 'CONFIRMED' },
  { id: 'booking-2', customerId: 'user-456', service: 'Laundry', date: '2024-01-22', status: 'IN_PROGRESS' },
];

const notificationTypes = [
  { value: 'booking_update', label: 'Booking Update' },
  { value: 'message', label: 'Message' },
  { value: 'promotion', label: 'Promotion' },
  { value: 'reminder', label: 'Reminder' },
];

export const SendNotificationScreen: React.FC = () => {
  const { sendPushNotification } = useNotificationStore();
  const [selectedCustomer, setSelectedCustomer] = useState(mockCustomers[0].id);
  const [selectedBooking, setSelectedBooking] = useState(mockBookings[0].id);
  const [type, setType] = useState(notificationTypes[0].value);
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [isSending, setIsSending] = useState(false);

  const handleSend = async () => {
    if (!title.trim() || !message.trim()) {
      Alert.alert('Missing Fields', 'Please enter a title and message.');
      return;
    }
    setIsSending(true);
    try {
      // Compose notification data
      const data = {
        type: type as 'booking_update' | 'message' | 'promotion' | 'reminder' | 'payment',
        title,
        body: message,
        bookingId: type === 'booking_update' || type === 'message' ? selectedBooking : undefined,
        userId: selectedCustomer,
      };
      const success = await sendPushNotification(data);
      if (success) {
        Alert.alert('Notification Sent', 'The notification was sent successfully.');
        setTitle('');
        setMessage('');
      } else {
        Alert.alert('Error', 'Failed to send notification.');
      }
    } catch (error) {
      Alert.alert('Error', 'An unexpected error occurred.');
    } finally {
      setIsSending(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Text style={styles.header}>Send Push Notification</Text>

        {/* Select Customer */}
        <Text style={styles.label}>Select Customer</Text>
        <View style={styles.selectList}>
          {mockCustomers.map((customer) => (
            <TouchableOpacity
              key={customer.id}
              style={[styles.selectItem, selectedCustomer === customer.id && styles.selectedItem]}
              onPress={() => setSelectedCustomer(customer.id)}
            >
              <Text style={styles.selectItemText}>{customer.name} ({customer.phone})</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Select Booking (if type is booking_update or message) */}
        {(type === 'booking_update' || type === 'message') && (
          <>
            <Text style={styles.label}>Select Booking</Text>
            <View style={styles.selectList}>
              {mockBookings
                .filter(b => b.customerId === selectedCustomer)
                .map((booking) => (
                  <TouchableOpacity
                    key={booking.id}
                    style={[styles.selectItem, selectedBooking === booking.id && styles.selectedItem]}
                    onPress={() => setSelectedBooking(booking.id)}
                  >
                    <Text style={styles.selectItemText}>{booking.service} ({booking.date}) - {booking.status}</Text>
                  </TouchableOpacity>
                ))}
            </View>
          </>
        )}

        {/* Notification Type */}
        <Text style={styles.label}>Notification Type</Text>
        <View style={styles.selectList}>
          {notificationTypes.map((nt) => (
            <TouchableOpacity
              key={nt.value}
              style={[styles.selectItem, type === nt.value && styles.selectedItem]}
              onPress={() => setType(nt.value)}
            >
              <Text style={styles.selectItemText}>{nt.label}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Title */}
        <Text style={styles.label}>Title</Text>
        <TextInput
          style={styles.input}
          value={title}
          onChangeText={setTitle}
          placeholder="Enter notification title"
        />

        {/* Message */}
        <Text style={styles.label}>Message</Text>
        <TextInput
          style={[styles.input, styles.textArea]}
          value={message}
          onChangeText={setMessage}
          placeholder="Enter notification message"
          multiline
          numberOfLines={4}
        />

        <Button
          title={isSending ? 'Sending...' : 'Send Notification'}
          onPress={handleSend}
          disabled={isSending}
          style={styles.sendButton}
        />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  content: {
    flex: 1,
    padding: spacing.lg,
  },
  header: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.lg,
    textAlign: 'center',
  },
  label: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    marginTop: spacing.md,
  },
  selectList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: spacing.sm,
  },
  selectItem: {
    backgroundColor: colors.white,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginRight: spacing.sm,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  selectedItem: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '11',
  },
  selectItemText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[900],
  },
  input: {
    backgroundColor: colors.white,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    color: colors.gray[900],
    marginBottom: spacing.sm,
  },
  textArea: {
    minHeight: 80,
    textAlignVertical: 'top',
  },
  sendButton: {
    marginTop: spacing.lg,
    backgroundColor: colors.primary,
  },
}); 