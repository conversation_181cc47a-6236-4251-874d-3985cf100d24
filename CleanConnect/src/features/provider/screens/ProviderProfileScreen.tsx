import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { useAuthStore } from '../../../store/useAuthStore';
import { router } from 'expo-router';
import { Button } from '../../../components/common/Button';
import { Input } from '../../../components/common/Input';
import { ProviderNavigation } from '../components/ProviderNavigation';

// Mock provider data
const providerData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+************',
  rating: 4.8,
  totalReviews: 28,
  completedJobs: 45,
  memberSince: 'March 2024',
  services: [
    { id: '1', name: 'House Cleaning', price: 50, active: true },
    { id: '2', name: 'Deep Cleaning', price: 80, active: true },
    { id: '3', name: 'Kitchen Cleaning', price: 40, active: true },
    { id: '4', name: 'Laundry Service', price: 25, active: false },
    { id: '5', name: 'Window Cleaning', price: 30, active: true },
  ],
  availability: {
    monday: { start: '09:00', end: '17:00', available: true },
    tuesday: { start: '09:00', end: '17:00', available: true },
    wednesday: { start: '09:00', end: '17:00', available: true },
    thursday: { start: '09:00', end: '17:00', available: true },
    friday: { start: '09:00', end: '17:00', available: true },
    saturday: { start: '10:00', end: '15:00', available: true },
    sunday: { start: '10:00', end: '15:00', available: false },
  },
};

export const ProviderProfileScreen: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profile, setProfile] = useState(providerData);
  const { logout } = useAuthStore();

  const handleSaveProfile = () => {
    setIsEditing(false);
    Alert.alert('Success', 'Profile updated successfully!');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Are you sure you want to logout?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Logout',
          style: 'destructive',
          onPress: async () => {
            await logout();
            router.replace('/onboarding');
          },
        },
      ]
    );
  };

  const toggleService = (serviceId: string) => {
    setProfile(prev => ({
      ...prev,
      services: prev.services.map(service =>
        service.id === serviceId
          ? { ...service, active: !service.active }
          : service
      ),
    }));
  };

  const toggleAvailability = (day: string) => {
    setProfile(prev => ({
      ...prev,
      availability: {
        ...prev.availability,
        [day]: {
          ...prev.availability[day as keyof typeof prev.availability],
          available: !prev.availability[day as keyof typeof prev.availability].available,
        },
      },
    }));
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Profile</Text>
        <Text style={styles.headerSubtitle}>Manage your account and services</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Profile Overview */}
        <View style={styles.section}>
          <View style={styles.profileCard}>
            <View style={styles.profileHeader}>
              <View style={styles.avatarContainer}>
                <Text style={styles.avatarText}>JD</Text>
              </View>
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{profile.name}</Text>
                <Text style={styles.profileEmail}>{profile.email}</Text>
                <View style={styles.ratingContainer}>
                  <Text style={styles.ratingText}>⭐ {profile.rating}</Text>
                  <Text style={styles.reviewsText}>({profile.totalReviews} reviews)</Text>
                </View>
              </View>
            </View>
            <View style={styles.profileStats}>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{profile.completedJobs}</Text>
                <Text style={styles.statLabel}>Jobs Completed</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statValue}>{profile.memberSince}</Text>
                <Text style={styles.statLabel}>Member Since</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Personal Information */}
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Personal Information</Text>
            <TouchableOpacity onPress={() => setIsEditing(!isEditing)}>
              <Text style={styles.editButton}>
                {isEditing ? 'Cancel' : 'Edit'}
              </Text>
            </TouchableOpacity>
          </View>
          <View style={styles.infoCard}>
            <Input
              label="Full Name"
              value={profile.name}
              onChangeText={(text) => setProfile({ ...profile, name: text })}
              disabled={!isEditing}
            />
            <Input
              label="Email"
              value={profile.email}
              onChangeText={(text) => setProfile({ ...profile, email: text })}
              disabled={!isEditing}
              keyboardType="email-address"
            />
            <Input
              label="Phone Number"
              value={profile.phone}
              onChangeText={(text) => setProfile({ ...profile, phone: text })}
              disabled={!isEditing}
              keyboardType="phone-pad"
            />
            {isEditing && (
              <Button
                title="Save Changes"
                onPress={handleSaveProfile}
                style={styles.saveButton}
              />
            )}
          </View>
        </View>

        {/* Services */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>My Services</Text>
          <View style={styles.servicesCard}>
            {profile.services.map((service) => (
              <View key={service.id} style={styles.serviceItem}>
                <View style={styles.serviceInfo}>
                  <Text style={styles.serviceName}>{service.name}</Text>
                  <Text style={styles.servicePrice}>${service.price}</Text>
                </View>
                <TouchableOpacity
                  style={[
                    styles.toggleButton,
                    service.active && styles.toggleButtonActive,
                  ]}
                  onPress={() => toggleService(service.id)}
                >
                  <Text style={[
                    styles.toggleText,
                    service.active && styles.toggleTextActive,
                  ]}>
                    {service.active ? 'Active' : 'Inactive'}
                  </Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        {/* Availability */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Availability</Text>
          <View style={styles.availabilityCard}>
            {Object.entries(profile.availability).map(([day, schedule]) => (
              <View key={day} style={styles.availabilityItem}>
                <View style={styles.dayInfo}>
                  <Text style={styles.dayName}>
                    {day.charAt(0).toUpperCase() + day.slice(1)}
                  </Text>
                  <Text style={styles.timeRange}>
                    {schedule.start} - {schedule.end}
                  </Text>
                </View>
                <TouchableOpacity
                  style={[
                    styles.availabilityToggle,
                    schedule.available && styles.availabilityToggleActive,
                  ]}
                  onPress={() => toggleAvailability(day)}
                >
                  <Text style={[
                    styles.availabilityText,
                    schedule.available && styles.availabilityTextActive,
                  ]}>
                    {schedule.available ? 'Available' : 'Unavailable'}
                  </Text>
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>

        {/* Account Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account</Text>
          <View style={styles.accountCard}>
            <TouchableOpacity style={styles.accountAction}>
              <Text style={styles.accountActionText}>Change Password</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.accountAction}>
              <Text style={styles.accountActionText}>Privacy Settings</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.accountAction}>
              <Text style={styles.accountActionText}>Help & Support</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.accountAction, styles.logoutAction]}
              onPress={handleLogout}
            >
              <Text style={[styles.accountActionText, styles.logoutText]}>
                Logout
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={styles.bottomSpacing} />
        
        {/* Navigation */}
        <ProviderNavigation />
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
  },
  content: {
    flex: 1,
  },
  section: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  editButton: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  profileCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  profileHeader: {
    flexDirection: 'row',
    marginBottom: spacing.lg,
  },
  avatarContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  avatarText: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
  },
  profileInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  profileName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  profileEmail: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    marginBottom: spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.warning,
    marginRight: spacing.xs,
  },
  reviewsText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
  },
  profileStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    paddingTop: spacing.lg,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  saveButton: {
    marginTop: spacing.lg,
  },
  servicesCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  serviceItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  servicePrice: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  toggleButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.gray[200],
  },
  toggleButtonActive: {
    backgroundColor: colors.success,
  },
  toggleText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontWeight: typography.weights.medium,
  },
  toggleTextActive: {
    color: colors.white,
  },
  availabilityCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  availabilityItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  dayInfo: {
    flex: 1,
  },
  dayName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    marginBottom: spacing.xs,
  },
  timeRange: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
  },
  availabilityToggle: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.gray[200],
  },
  availabilityToggleActive: {
    backgroundColor: colors.success,
  },
  availabilityText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontWeight: typography.weights.medium,
  },
  availabilityTextActive: {
    color: colors.white,
  },
  accountCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  accountAction: {
    padding: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  accountActionText: {
    fontSize: typography.sizes.md,
    color: colors.gray[900],
  },
  logoutAction: {
    borderBottomWidth: 0,
  },
  logoutText: {
    color: colors.error,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
}); 