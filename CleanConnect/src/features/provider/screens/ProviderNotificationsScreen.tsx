import React, { useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { useProviderStore } from '../../../store/providerStore';
import { ProviderNavigation } from '../components/ProviderNavigation';

// Mock notifications data matching the design
const mockNotifications = [
  {
    id: '1',
    type: 'BOOKING_REQUEST' as const,
    title: '<PERSON>akatullah update your booking',
    message: 'New booking request received',
    customerName: 'Barakatullah',
    customerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    timestamp: '3 min ago',
    isRead: false,
    actionRequired: false,
  },
  {
    id: '2',
    type: 'PAYMENT' as const,
    title: '<PERSON>aka<PERSON><PERSON> accept request - pay $5.00',
    message: 'Payment received for completed service',
    customerName: 'Barakatullah',
    customerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    amount: 5.00,
    timestamp: '3 min ago',
    isRead: false,
    actionRequired: true,
  },
  {
    id: '3',
    type: 'BOOKING_REQUEST' as const,
    title: 'Barakatullah request home... price $108',
    message: 'New home cleaning request',
    customerName: 'Barakatullah',
    customerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    amount: 108,
    timestamp: '5 min ago',
    isRead: false,
    actionRequired: true,
  },
  {
    id: '4',
    type: 'BOOKING_REQUEST' as const,
    title: 'Zahid Hassan request home... price $132',
    message: 'New home cleaning request',
    customerName: 'Zahid Hassan',
    customerAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    amount: 132,
    timestamp: '5 min ago',
    isRead: false,
    actionRequired: true,
  },
  {
    id: '5',
    type: 'BOOKING_REQUEST' as const,
    title: 'Shahid Miah request to Pool... price $112',
    message: 'Pool cleaning service request',
    customerName: 'Shahid Miah',
    customerAvatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    amount: 112,
    timestamp: '5 min ago',
    isRead: false,
    actionRequired: true,
  },
  {
    id: '6',
    type: 'BOOKING_REQUEST' as const,
    title: 'Moaine Ali request home... price $132',
    message: 'Home cleaning service request',
    customerName: 'Moaine Ali',
    customerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    amount: 132,
    timestamp: '5 min ago',
    isRead: false,
    actionRequired: true,
  },
  {
    id: '7',
    type: 'PAYMENT' as const,
    title: 'You withdrew money $220',
    message: 'Money withdrawal successful',
    amount: 220,
    timestamp: '1 hour ago',
    isRead: false,
    actionRequired: false,
  },
  {
    id: '8',
    type: 'REVIEW' as const,
    title: 'Ralph Edwards review at ⭐ 4.94',
    message: 'New review received',
    customerName: 'Ralph Edwards',
    customerAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 4.94,
    timestamp: '3 min ago',
    isRead: false,
    actionRequired: false,
  },
];

export const ProviderNotificationsScreen: React.FC = () => {
  const {
    notifications,
    setNotifications,
    markNotificationAsRead,
    markAllNotificationsAsRead,
  } = useProviderStore();

  useEffect(() => {
    // Initialize mock notifications
    setNotifications(mockNotifications);
  }, []);

  const handleNotificationPress = (notification: typeof mockNotifications[0]) => {
    markNotificationAsRead(notification.id);
    
    if (notification.type === 'BOOKING_REQUEST' && notification.actionRequired) {
      // Navigate to booking details or handle booking request
      console.log('Handle booking request:', notification.id);
    }
  };

  const handleAcceptRequest = (notificationId: string) => {
    // Handle accept request
    console.log('Accept request:', notificationId);
    markNotificationAsRead(notificationId);
  };

  const handleDeclineRequest = (notificationId: string) => {
    // Handle decline request
    console.log('Decline request:', notificationId);
    markNotificationAsRead(notificationId);
  };

  const handleMarkAllAsRead = () => {
    markAllNotificationsAsRead();
  };

  const renderNotificationIcon = (type: string) => {
    switch (type) {
      case 'BOOKING_REQUEST':
        return '📋';
      case 'PAYMENT':
        return '💰';
      case 'REVIEW':
        return '⭐';
      case 'SYSTEM':
        return '🔔';
      default:
        return '📱';
    }
  };

  const renderNotification = (notification: typeof mockNotifications[0]) => (
    <TouchableOpacity
      key={notification.id}
      style={[
        styles.notificationCard,
        !notification.isRead && styles.unreadNotification,
      ]}
      onPress={() => handleNotificationPress(notification)}
    >
      <View style={styles.notificationContent}>
        <View style={styles.notificationLeft}>
          {notification.customerAvatar ? (
            <Image
              source={{ uri: notification.customerAvatar }}
              style={styles.customerAvatar}
            />
          ) : (
            <View style={styles.notificationIcon}>
              <Text style={styles.notificationIconText}>
                {renderNotificationIcon(notification.type)}
              </Text>
            </View>
          )}
          <View style={styles.notificationText}>
            <Text style={styles.notificationTitle} numberOfLines={2}>
              {notification.title}
            </Text>
            <Text style={styles.notificationTime}>{notification.timestamp}</Text>
          </View>
        </View>
        
        {notification.actionRequired && (
          <View style={styles.notificationActions}>
            <TouchableOpacity
              style={styles.acceptButton}
              onPress={() => handleAcceptRequest(notification.id)}
            >
              <Text style={styles.acceptButtonText}>Accept</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.declineButton}
              onPress={() => handleDeclineRequest(notification.id)}
            >
              <Text style={styles.declineButtonText}>Decline</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>
      
      {!notification.isRead && <View style={styles.unreadDot} />}
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Notification</Text>
        <TouchableOpacity onPress={handleMarkAllAsRead}>
          <Text style={styles.markAllReadText}>Mark all as read</Text>
        </TouchableOpacity>
      </View>

      {/* Filter Tabs */}
      <View style={styles.filterContainer}>
        <TouchableOpacity style={[styles.filterTab, styles.activeFilterTab]}>
          <Text style={[styles.filterText, styles.activeFilterText]}>All</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.filterTab}>
          <Text style={styles.filterText}>Unread</Text>
        </TouchableOpacity>
      </View>

      {/* Notifications List */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {(notifications.length > 0 ? notifications : mockNotifications).map(renderNotification)}
      </ScrollView>

      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  backIcon: {
    fontSize: 20,
    color: colors.text.primary,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
  },
  markAllReadText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  filterContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  filterTab: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    marginRight: spacing.sm,
  },
  activeFilterTab: {
    backgroundColor: colors.primary,
  },
  filterText: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.medium,
  },
  activeFilterText: {
    color: colors.white,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  notificationCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    marginVertical: spacing.sm,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    position: 'relative',
  },
  unreadNotification: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  notificationContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  notificationLeft: {
    flexDirection: 'row',
    flex: 1,
    alignItems: 'flex-start',
  },
  customerAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.md,
  },
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  notificationIconText: {
    fontSize: 18,
  },
  notificationText: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: typography.sizes.md,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.medium,
    marginBottom: spacing.xs,
    lineHeight: typography.sizes.md * typography.lineHeights.normal,
  },
  notificationTime: {
    fontSize: typography.sizes.sm,
    color: colors.text.tertiary,
    fontFamily: typography.fontFamily.regular,
  },
  notificationActions: {
    flexDirection: 'column',
    gap: spacing.sm,
    marginLeft: spacing.md,
  },
  acceptButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.primary,
    minWidth: 70,
    alignItems: 'center',
  },
  acceptButtonText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
  declineButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
    minWidth: 70,
    alignItems: 'center',
  },
  declineButtonText: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.semibold,
  },
  unreadDot: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
});
