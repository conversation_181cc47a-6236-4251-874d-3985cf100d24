import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { Button } from '../../../components/common/Button';
import { ProviderNavigation } from '../components/ProviderNavigation';

// Mock data for provider dashboard
const dashboardStats = {
  totalEarnings: 1250,
  thisMonth: 320,
  completedJobs: 45,
  pendingJobs: 3,
  rating: 4.8,
  totalReviews: 28,
};

const recentBookings = [
  {
    id: '1',
    customerName: '<PERSON>',
    service: 'House Cleaning',
    date: 'Today',
    time: '10:00 AM',
    status: 'IN_PROGRESS',
    amount: 50,
    address: '123 Main St, Banjul',
  },
  {
    id: '2',
    customerName: '<PERSON>',
    service: 'Deep Cleaning',
    date: 'Tomorrow',
    time: '02:00 PM',
    status: 'CONFIRMED',
    amount: 80,
    address: '456 Business Ave, Banjul',
  },
  {
    id: '3',
    customerName: 'Fatou Diallo',
    service: 'Kitchen Cleaning',
    date: 'Yesterday',
    time: '11:00 AM',
    status: 'COMPLETED',
    amount: 40,
    address: '789 Residential Rd, Banjul',
  },
];

const quickActions = [
  { id: '1', title: 'View Bookings', icon: '📋', color: colors.primary },
  { id: '2', title: 'Earnings', icon: '💰', color: colors.secondary },
  { id: '3', title: 'Schedule', icon: '📅', color: colors.accent },
  { id: '4', title: 'Profile', icon: '👤', color: colors.warning },
];

export const ProviderDashboardScreen: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'bookings' | 'earnings'>('overview');

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return colors.success;
      case 'IN_PROGRESS':
        return colors.warning;
      case 'CONFIRMED':
        return colors.primary;
      default:
        return colors.gray[500];
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Completed';
      case 'IN_PROGRESS':
        return 'In Progress';
      case 'CONFIRMED':
        return 'Confirmed';
      default:
        return status;
    }
  };

  const handleQuickAction = (action: typeof quickActions[0]) => {
    switch (action.title) {
      case 'View Bookings':
        router.push('/provider/bookings');
        break;
      case 'Earnings':
        router.push('/provider/earnings');
        break;
      case 'Schedule':
        console.log('Navigate to schedule');
        break;
      case 'Profile':
        router.push('/provider/profile');
        break;
      default:
        console.log('Quick action:', action.title);
    }
  };

  const handleBookingPress = (booking: typeof recentBookings[0]) => {
    console.log('Booking pressed:', booking.id);
    // Navigate to booking details
  };

  const handleViewAllBookings = () => {
    router.push('/provider/bookings');
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View>
          <Text style={styles.greeting}>Good morning!</Text>
          <Text style={styles.providerName}>John Doe</Text>
        </View>
        <TouchableOpacity style={styles.profileButton}>
          <Text style={styles.profileIcon}>👤</Text>
        </TouchableOpacity>
      </View>

      {/* Stats Overview */}
      <View style={styles.statsContainer}>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>${dashboardStats.totalEarnings}</Text>
          <Text style={styles.statLabel}>Total Earnings</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>${dashboardStats.thisMonth}</Text>
          <Text style={styles.statLabel}>This Month</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{dashboardStats.completedJobs}</Text>
          <Text style={styles.statLabel}>Completed Jobs</Text>
        </View>
        <View style={styles.statCard}>
          <Text style={styles.statValue}>{dashboardStats.pendingJobs}</Text>
          <Text style={styles.statLabel}>Pending Jobs</Text>
        </View>
      </View>

      {/* Rating */}
      <View style={styles.ratingContainer}>
        <View style={styles.ratingCard}>
          <Text style={styles.ratingValue}>⭐ {dashboardStats.rating}</Text>
          <Text style={styles.ratingLabel}>
            {dashboardStats.totalReviews} reviews
          </Text>
        </View>
      </View>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsGrid}>
          {quickActions.map((action) => (
            <TouchableOpacity
              key={action.id}
              style={styles.quickActionCard}
              onPress={() => handleQuickAction(action)}
            >
              <Text style={styles.quickActionIcon}>{action.icon}</Text>
              <Text style={styles.quickActionTitle}>{action.title}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      {/* Recent Bookings */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Recent Bookings</Text>
          <TouchableOpacity onPress={handleViewAllBookings}>
            <Text style={styles.viewAllText}>View All</Text>
          </TouchableOpacity>
        </View>
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          {recentBookings.map((booking) => (
            <TouchableOpacity
              key={booking.id}
              style={styles.bookingCard}
              onPress={() => handleBookingPress(booking)}
            >
              <View style={styles.bookingHeader}>
                <Text style={styles.customerName}>{booking.customerName}</Text>
                <View style={[
                  styles.statusBadge,
                  { backgroundColor: getStatusColor(booking.status) }
                ]}>
                  <Text style={styles.statusText}>
                    {getStatusText(booking.status)}
                  </Text>
                </View>
              </View>
              <Text style={styles.serviceName}>{booking.service}</Text>
              <View style={styles.bookingDetails}>
                <Text style={styles.bookingDetail}>📅 {booking.date}</Text>
                <Text style={styles.bookingDetail}>⏰ {booking.time}</Text>
              </View>
              <Text style={styles.bookingAddress}>{booking.address}</Text>
              <View style={styles.bookingFooter}>
                <Text style={styles.bookingAmount}>${booking.amount}</Text>
                <Button
                  title="View Details"
                  onPress={() => handleBookingPress(booking)}
                  size="small"
                  style={styles.viewDetailsButton}
                />
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Bottom Spacing */}
      <View style={styles.bottomSpacing} />
      
      {/* Navigation */}
      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    backgroundColor: colors.white,
  },
  greeting: {
    fontSize: typography.sizes.lg,
    color: colors.gray[600],
  },
  providerName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  profileButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.neutral,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileIcon: {
    fontSize: 20,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  statCard: {
    width: '48%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.sm,
    marginHorizontal: '1%',
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  statValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
  },
  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    textAlign: 'center',
  },
  ratingContainer: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.md,
  },
  ratingCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    alignItems: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  ratingValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.warning,
    marginBottom: spacing.xs,
  },
  ratingLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
  },
  section: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  viewAllText: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  quickActionCard: {
    width: '48%',
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    alignItems: 'center',
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  quickActionIcon: {
    fontSize: 32,
    marginBottom: spacing.sm,
  },
  quickActionTitle: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.gray[700],
    textAlign: 'center',
  },
  bookingCard: {
    width: 280,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    marginRight: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  bookingHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  customerName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
  },
  statusBadge: {
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  statusText: {
    color: colors.white,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
  },
  serviceName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.primary,
    marginBottom: spacing.sm,
  },
  bookingDetails: {
    flexDirection: 'row',
    marginBottom: spacing.sm,
  },
  bookingDetail: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    marginRight: spacing.md,
  },
  bookingAddress: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    marginBottom: spacing.md,
  },
  bookingFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  bookingAmount: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.primary,
  },
  viewDetailsButton: {
    width: 'auto',
    paddingHorizontal: spacing.md,
  },
  bottomSpacing: {
    height: spacing.xl,
  },
}); 