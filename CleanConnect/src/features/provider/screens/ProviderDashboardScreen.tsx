import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Dimensions,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { ProviderNavigation } from '../components/ProviderNavigation';
import { useProviderStore } from '../../../store/providerStore';

const { width } = Dimensions.get('window');

// Mock data for provider dashboard - matching the design
const mockServiceRequests = [
  {
    id: '1',
    customerName: '<PERSON>',
    customerAvatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    service: 'Post-Construction Clean',
    serviceType: 'Premium' as const,
    date: '5 Dec 24',
    time: '10:00 AM',
    duration: '3 hours',
    location: 'Barcelona, Spain',
    address: '123 Construction Site, Barcelona',
    price: 108,
    status: 'PENDING' as const,
    description: 'Post-construction cleaning for new office space',
    image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=300&h=200&fit=crop',
    createdAt: new Date().toISOString(),
  },
  {
    id: '2',
    customerName: 'Michael Chen',
    customerAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    service: 'Window Cleaning',
    serviceType: 'Standard' as const,
    date: '5 Dec 24',
    time: '02:00 PM',
    duration: '2 hours',
    location: 'Barcelona, Spain',
    address: '456 Office Building, Barcelona',
    price: 108,
    status: 'PENDING' as const,
    description: 'Professional window cleaning service',
    image: 'https://images.unsplash.com/photo-1527515637462-cff94eecc1ac?w=300&h=200&fit=crop',
    createdAt: new Date().toISOString(),
  },
  {
    id: '3',
    customerName: 'Emma Wilson',
    customerAvatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    service: 'Carpet & Rug Cleaning',
    serviceType: 'Premium' as const,
    date: '6 Dec 24',
    time: '09:00 AM',
    duration: '4 hours',
    location: 'Barcelona, Spain',
    address: '789 Residential Complex, Barcelona',
    price: 108,
    status: 'PENDING' as const,
    description: 'Deep carpet and rug cleaning',
    image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300&h=200&fit=crop',
    createdAt: new Date().toISOString(),
  },
  {
    id: '4',
    customerName: 'David Rodriguez',
    customerAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    service: 'Kitchen Cleaning',
    serviceType: 'Standard' as const,
    date: '6 Dec 24',
    time: '11:00 AM',
    duration: '2 hours',
    location: 'Barcelona, Spain',
    address: '321 Restaurant District, Barcelona',
    price: 108,
    status: 'PENDING' as const,
    description: 'Professional kitchen deep cleaning',
    image: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=300&h=200&fit=crop',
    createdAt: new Date().toISOString(),
  },
];

export const ProviderDashboardScreen: React.FC = () => {
  const {
    profile,
    earnings,
    serviceRequests,
    pendingRequests,
    selectedPeriod,
    setServiceRequests,
    setEarnings,
    setProfile,
    setSelectedPeriod,
    acceptServiceRequest,
    declineServiceRequest,
  } = useProviderStore();

  useEffect(() => {
    // Initialize mock data
    setProfile({
      id: '1',
      name: 'Barakatullah',
      email: '<EMAIL>',
      phone: '+************',
      rating: 4.8,
      totalReviews: 156,
      completedJobs: 89,
      joinedDate: '2023-01-15',
      services: ['House Cleaning', 'Deep Cleaning', 'Window Cleaning'],
      location: 'Barcelona, Spain',
      isVerified: true,
      isOnline: true,
    });

    setEarnings({
      totalEarnings: 12500,
      currentMonth: 1000,
      lastMonth: 850,
      thisWeek: 320,
      today: 108,
      completedJobs: 89,
      pendingJobs: 4,
      rating: 4.8,
      totalReviews: 156,
    });

    setServiceRequests(mockServiceRequests);
  }, []);

  const handleAcceptRequest = (requestId: string) => {
    acceptServiceRequest(requestId);
  };

  const handleDeclineRequest = (requestId: string) => {
    declineServiceRequest(requestId);
  };

  const handleViewDetails = (requestId: string) => {
    router.push(`/provider/booking-details/${requestId}`);
  };

  const handleSeeAll = () => {
    router.push('/provider/bookings');
  };

  const formatCurrency = (amount: number) => {
    return `$${amount.toLocaleString()}`;
  };

  const renderServiceRequestCard = (request: typeof mockServiceRequests[0]) => (
    <View key={request.id} style={styles.serviceRequestCard}>
      <View style={styles.serviceRequestHeader}>
        <View style={styles.serviceRequestInfo}>
          <Text style={styles.serviceRequestLocation}>📍 {request.location}</Text>
          <Text style={styles.serviceRequestTitle}>{request.service}</Text>
          <Text style={styles.serviceRequestDetails}>
            {request.date} • {request.duration}
          </Text>
        </View>
        <View style={styles.serviceRequestActions}>
          <TouchableOpacity
            style={styles.declineButton}
            onPress={() => handleDeclineRequest(request.id)}
          >
            <Text style={styles.declineButtonText}>Decline</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.acceptButton}
            onPress={() => handleAcceptRequest(request.id)}
          >
            <Text style={styles.acceptButtonText}>Accept</Text>
          </TouchableOpacity>
        </View>
      </View>
      <View style={styles.serviceRequestImage}>
        <Text style={styles.serviceRequestImagePlaceholder}>🏠</Text>
      </View>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerLeft}>
          <Text style={styles.greeting}>👋</Text>
          <Text style={styles.providerName}>{profile?.name || 'Barakatullah'}</Text>
        </View>
        <TouchableOpacity style={styles.notificationButton}>
          <Text style={styles.notificationIcon}>🔔</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Earnings Summary */}
        <View style={styles.earningsContainer}>
          <View style={styles.earningsHeader}>
            <Text style={styles.earningsLabel}>Earnings Summary</Text>
            <Text style={styles.currentMonth}>Current Month</Text>
          </View>
          <Text style={styles.earningsAmount}>{formatCurrency(earnings.currentMonth)}</Text>
          <TouchableOpacity style={styles.periodSelector}>
            <Text style={styles.periodText}>Monthly ▼</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.viewDetailsButton}>
            <Text style={styles.viewDetailsText}>View Details</Text>
          </TouchableOpacity>
        </View>

        {/* Get Request Accept Section */}
        <View style={styles.requestSection}>
          <View style={styles.requestHeader}>
            <Text style={styles.requestTitle}>Get Request Accept</Text>
            <TouchableOpacity onPress={handleSeeAll}>
              <Text style={styles.seeAllText}>See all</Text>
            </TouchableOpacity>
          </View>

          {pendingRequests.slice(0, 4).map(renderServiceRequestCard)}
        </View>
      </ScrollView>

      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: colors.primary,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  greeting: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  providerName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.white,
    fontFamily: typography.fontFamily.bold,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.white + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationIcon: {
    fontSize: 20,
  },
  content: {
    flex: 1,
  },
  earningsContainer: {
    backgroundColor: colors.white,
    margin: spacing.lg,
    borderRadius: 16,
    padding: spacing.xl,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  earningsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  earningsLabel: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
  },
  currentMonth: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
  },
  earningsAmount: {
    fontSize: typography.sizes.xxxl,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
  },
  periodSelector: {
    backgroundColor: colors.primary + '15',
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    alignSelf: 'flex-start',
    marginBottom: spacing.md,
  },
  periodText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  viewDetailsButton: {
    alignSelf: 'flex-end',
  },
  viewDetailsText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  requestSection: {
    paddingHorizontal: spacing.lg,
    marginBottom: spacing.xl,
  },
  requestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  requestTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
  },
  seeAllText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  },
  serviceRequestCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
  },
  serviceRequestHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  serviceRequestInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  serviceRequestLocation: {
    fontSize: typography.sizes.xs,
    color: colors.text.tertiary,
    fontFamily: typography.fontFamily.regular,
    marginBottom: spacing.xs,
  },
  serviceRequestTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.xs,
  },
  serviceRequestDetails: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
  },
  serviceRequestActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },
  declineButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.gray[300],
  },
  declineButtonText: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.medium,
  },
  acceptButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 8,
    backgroundColor: colors.primary,
  },
  acceptButtonText: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontFamily: typography.fontFamily.medium,
  },
  serviceRequestImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },
  serviceRequestImagePlaceholder: {
    fontSize: 24,
  },
});