import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native';
import { router, usePathname } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

const { width: screenWidth } = Dimensions.get('window');

interface NavigationItem {
  id: string;
  label: string;
  icon: string;
  route: string;
}

const navigationItems: NavigationItem[] = [
  {
    id: 'home',
    label: 'Home',
    icon: '🏠',
    route: '/customer/home',
  },
  {
    id: 'services',
    label: 'Services',
    icon: '📦',
    route: '/customer/booking/service-selection',
  },
  {
    id: 'orders',
    label: 'Orders',
    icon: '📋',
    route: '/customer/bookings',
  },
  {
    id: 'profile',
    label: 'Profile',
    icon: '👤',
    route: '/customer/profile',
  },
];

export const CustomerNavigation: React.FC = () => {
  const pathname = usePathname();

  const handleNavigation = (route: string) => {
    router.push(route as any);
  };

  const isActiveRoute = (route: string): boolean => {
    // Handle exact matches
    if (pathname === route) return true;

    // Handle nested routes
    if (route === '/customer/home' && pathname.startsWith('/customer') &&
        !pathname.includes('/booking') && !pathname.includes('/profile') && !pathname.includes('/bookings')) {
      return true;
    }

    if (route === '/customer/booking/service-selection' && pathname.includes('/customer/booking')) {
      return true;
    }

    if (route === '/customer/bookings' && pathname.includes('/customer/bookings')) {
      return true;
    }

    if (route === '/customer/profile' && pathname.includes('/customer/profile')) {
      return true;
    }

    return false;
  };

  return (
    <View style={styles.container}>
      {navigationItems.map((item) => {
        const isActive = isActiveRoute(item.route);

        return (
          <TouchableOpacity
            key={item.id}
            style={styles.navItem}
            onPress={() => handleNavigation(item.route)}
            activeOpacity={0.7}
          >
            <View style={styles.iconContainer}>
              <Text style={[
                styles.navIcon,
                isActive && styles.navIconActive,
              ]}>
                {item.icon}
              </Text>
            </View>
            <Text style={[
              styles.navLabel,
              isActive && styles.navLabelActive,
            ]}>
              {item.label}
            </Text>
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

// Calculate responsive dimensions
const getResponsiveDimensions = () => {
  const isSmallScreen = screenWidth < 375;
  const isMediumScreen = screenWidth >= 375 && screenWidth < 414;
  const isLargeScreen = screenWidth >= 414;

  return {
    containerPadding: isSmallScreen ? spacing.xs : spacing.sm,
    itemPadding: isSmallScreen ? spacing.xs : spacing.sm,
    iconSize: isSmallScreen ? 20 : isMediumScreen ? 22 : 24,
    fontSize: isSmallScreen ? typography.sizes.xs - 1 : typography.sizes.xs,
    iconMargin: isSmallScreen ? spacing.xs / 2 : spacing.xs,
  };
};

const responsiveDims = getResponsiveDimensions();

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    paddingVertical: responsiveDims.containerPadding,
    paddingHorizontal: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    // Ensure safe area handling for bottom navigation
    paddingBottom: responsiveDims.containerPadding + spacing.xs,
  } as ViewStyle,
  navItem: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: responsiveDims.itemPadding,
    paddingHorizontal: spacing.xs,
    minHeight: 60, // Ensure consistent touch target
  } as ViewStyle,
  iconContainer: {
    position: 'relative',
    marginBottom: responsiveDims.iconMargin,
    alignItems: 'center',
    justifyContent: 'center',
    width: responsiveDims.iconSize + 8,
    height: responsiveDims.iconSize + 8,
  } as ViewStyle,
  navIcon: {
    fontSize: responsiveDims.iconSize,
    textAlign: 'center',
    lineHeight: responsiveDims.iconSize + 2,
  } as TextStyle,
  navIconActive: {
    transform: [{ scale: 1.1 }],
  } as TextStyle,
  navLabel: {
    fontSize: responsiveDims.fontSize,
    color: colors.text.tertiary,
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
    lineHeight: responsiveDims.fontSize * typography.lineHeights.tight,
    marginTop: spacing.xs / 2,
  } as TextStyle,
  navLabelActive: {
    color: colors.primary,
    fontWeight: typography.weights.semibold,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
  badge: {
    position: 'absolute',
    top: -4,
    right: -8,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 18,
    height: 18,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs / 2,
    borderWidth: 2,
    borderColor: colors.white,
  } as ViewStyle,
  badgeText: {
    fontSize: typography.sizes.xs - 2,
    color: colors.white,
    fontWeight: typography.weights.bold,
    fontFamily: typography.fontFamily.bold,
    lineHeight: typography.sizes.xs,
  } as TextStyle,
});
