# TopNavbar Component

A reusable top navigation bar component for the CleanConnect mobile app with consistent layout and behavior across all customer screens.

## 📍 Layout Specification (LTR)

```
[Back] [🏠 CleanConnect]     [Screen Title]     [🔔] [🛒]
```

- **Left**: Back button (optional) + App logo
- **Center**: Screen title (centered)
- **Right**: Notification icon (optional) + Shopping cart icon (optional)

## 🧩 Props Interface

```typescript
interface TopNavbarProps {
  title: string;                    // Screen title (required)
  showBack?: boolean;               // Show back button (default: false)
  onBackPress?: () => void;         // Custom back handler (default: router.back())
  showNotification?: boolean;       // Show notification icon (default: false)
  showCart?: boolean;               // Show cart icon (default: false)
  backgroundColor?: string;         // Custom background color
  titleColor?: string;              // Custom title color
  iconColor?: string;               // Custom icon color (default: '#000')
}
```

## 🚀 Usage Examples

### Basic Usage
```tsx
import { TopNavbar } from '../../components/TopNavbar';

// Simple navbar with just title
<TopNavbar title="Home" />
```

### With Back Button
```tsx
// Navbar with back navigation
<TopNavbar 
  title="Service Details" 
  showBack={true} 
/>
```

### With Custom Back Handler
```tsx
// Custom back button behavior
<TopNavbar 
  title="Booking" 
  showBack={true}
  onBackPress={() => {
    // Custom logic before going back
    saveData();
    router.back();
  }}
/>
```

### Full Featured
```tsx
// All features enabled
<TopNavbar 
  title="Services"
  showBack={true}
  showNotification={true}
  showCart={true}
/>
```

### Custom Styling
```tsx
// Custom colors
<TopNavbar 
  title="Custom Theme"
  showBack={true}
  showNotification={true}
  backgroundColor={colors.primary}
  titleColor={colors.white}
  iconColor={colors.white}
/>
```

## 📌 Key Features

### ✅ **Sticky Behavior**
- Automatically stays at the top when content scrolls
- Uses proper z-index and elevation for layering
- Safe area handling for different devices

### ✅ **Smart Notifications**
- Automatically displays notification badge count
- Integrates with `useNotificationStore`
- Badge shows count up to 99+

### ✅ **Responsive Design**
- Adapts to different screen sizes
- Proper touch targets (44px minimum)
- Consistent spacing using design system constants

### ✅ **Accessibility**
- Proper hit slop for touch targets
- Screen reader support
- Focus management

## 🎨 Design System Integration

### **Colors**
- Uses `colors.white` for default background
- Uses `colors.primary` for logo text
- Uses `colors.text.primary` for title
- Uses `colors.error` for notification badge

### **Typography**
- Uses `textStyles.h4` for title
- Uses `textStyles.labelLarge` for logo
- Uses `textStyles.caption` for badge count

### **Spacing**
- Uses `spacing.layout.container` for horizontal padding
- Uses `spacing.md` for vertical padding
- Uses `spacing.sm` for element gaps

## 🔧 Implementation in Screens

### Replace Existing Headers
```tsx
// Before (old header implementation)
<View style={styles.header}>
  <TouchableOpacity onPress={() => router.back()}>
    <Ionicons name="chevron-back" size={24} />
  </TouchableOpacity>
  <Text style={styles.title}>Screen Title</Text>
  <TouchableOpacity onPress={handleNotification}>
    <Ionicons name="notifications" size={24} />
  </TouchableOpacity>
</View>

// After (using TopNavbar)
<TopNavbar 
  title="Screen Title"
  showBack={true}
  showNotification={true}
/>
```

### Screen Layout Structure
```tsx
export const MyScreen: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* Top Navigation */}
      <TopNavbar 
        title="My Screen"
        showBack={true}
        showNotification={true}
        showCart={true}
      />
      
      {/* Screen Content */}
      <ScrollView style={styles.content}>
        {/* Your content here */}
      </ScrollView>
      
      {/* Bottom Navigation */}
      <CustomerNavigation />
    </View>
  );
};
```

## 📱 Currently Implemented In

- ✅ **ServiceDetailScreen** - Full featured with back, notifications, and cart
- ✅ **ServiceSelectionScreen** - Full featured navigation
- ✅ **SearchScreen** - With back and action icons
- ✅ **NotificationCenterScreen** - With back and cart icons

## 🔄 Navigation Behavior

### **Default Back Action**
- Calls `router.back()` by default
- Works with Expo Router navigation stack

### **Custom Back Actions**
- Use `onBackPress` prop for custom logic
- Useful for save confirmations, form validation, etc.

### **Icon Actions**
- **Notification Icon**: Navigates to `/customer/notifications`
- **Cart Icon**: Navigates to `/customer/bookings`
- Both actions include console logging for debugging

## 🎯 Benefits

1. **Consistency**: Uniform header across all screens
2. **Maintainability**: Single component to update for header changes
3. **Accessibility**: Built-in accessibility features
4. **Performance**: Optimized rendering and touch handling
5. **Flexibility**: Customizable while maintaining consistency
6. **Integration**: Seamless integration with existing design system

## 🚧 Future Enhancements

- [ ] Search functionality in navbar
- [ ] Dynamic title updates
- [ ] Animation support for transitions
- [ ] Theme switching support
- [ ] Cart item count badge
- [ ] Custom action buttons support
