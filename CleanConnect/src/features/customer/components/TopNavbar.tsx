import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  SafeAreaView,
  StatusBar,
  Platform,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useNotificationStore } from '../../../store/useNotificationStore';
import { topNavbarStyles } from './TopNavbar.styles';

export interface TopNavbarProps {
  title: string;
  showBack?: boolean;
  onBackPress?: () => void;
  showNotification?: boolean;
  showCart?: boolean;
  backgroundColor?: string;
  titleColor?: string;
  iconColor?: string;
}

export const TopNavbar: React.FC<TopNavbarProps> = ({
  title,
  showBack = false,
  onBackPress,
  showNotification = false,
  showCart = false,
  backgroundColor,
  titleColor,
  iconColor = '#000',
}) => {
  const { unreadCount } = useNotificationStore();

  const handleBackPress = () => {
    if (onBackPress) {
      onBackPress();
    } else {
      router.back();
    }
  };

  const handleNotificationPress = () => {
    console.log('Notification pressed');
    router.push('/customer/notifications');
  };

  const handleCartPress = () => {
    console.log('Cart pressed');
    router.push('/customer/bookings');
  };

  const dynamicContainerStyle: ViewStyle = backgroundColor ? { backgroundColor } : {};
  const dynamicTitleStyle: TextStyle = titleColor ? { color: titleColor } : {};

  return (
    <>
      {/* Status Bar */}
      <StatusBar
        barStyle={Platform.OS === 'ios' ? 'dark-content' : 'default'}
        backgroundColor={backgroundColor || topNavbarStyles.container.backgroundColor}
      />
      
      <SafeAreaView style={[topNavbarStyles.safeArea as ViewStyle, dynamicContainerStyle]}>
        <View style={[topNavbarStyles.container as ViewStyle, dynamicContainerStyle]}>
          {/* Left Section: Back Button + Logo */}
          <View style={topNavbarStyles.leftSection as ViewStyle}>
            {showBack && (
              <TouchableOpacity
                style={topNavbarStyles.backButton as ViewStyle}
                onPress={handleBackPress}
                activeOpacity={0.7}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <Ionicons
                  name="chevron-back"
                  size={24}
                  color={iconColor}
                />
              </TouchableOpacity>
            )}

            {/* App Logo */}
            <View style={topNavbarStyles.logoContainer as ViewStyle}>
              <Text style={topNavbarStyles.logoIcon as TextStyle}>🏠</Text>
              
            </View>
          </View>

          {/* Center Section: Title */}
          <View style={topNavbarStyles.centerSection as ViewStyle}>
            <Text
              style={[topNavbarStyles.title as TextStyle, dynamicTitleStyle]}
              numberOfLines={1}
              ellipsizeMode="tail"
            >
              {title}
            </Text>
          </View>

          {/* Right Section: Notification + Cart Icons */}
          <View style={topNavbarStyles.rightSection as ViewStyle}>
            {showNotification && (
              <TouchableOpacity
                style={topNavbarStyles.actionButton as ViewStyle}
                onPress={handleNotificationPress}
                activeOpacity={0.7}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <View style={topNavbarStyles.iconContainer as ViewStyle}>
                  <Ionicons
                    name="notifications-outline"
                    size={24}
                    color={iconColor}
                  />
                  {unreadCount > 0 && (
                    <View style={topNavbarStyles.notificationBadge as ViewStyle}>
                      <Text style={topNavbarStyles.notificationCount as TextStyle}>
                        {unreadCount > 99 ? '99+' : unreadCount}
                      </Text>
                    </View>
                  )}
                </View>
              </TouchableOpacity>
            )}

            {showCart && (
              <TouchableOpacity
                style={topNavbarStyles.actionButton as ViewStyle}
                onPress={handleCartPress}
                activeOpacity={0.7}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <View style={topNavbarStyles.iconContainer as ViewStyle}>
                  <Ionicons
                    name="bag-outline"
                    size={24}
                    color={iconColor}
                  />
                  {/* Optional: Add cart item count badge here */}
                </View>
              </TouchableOpacity>
            )}

            {/* Spacer to maintain layout when no icons are shown */}
            {!showNotification && !showCart && (
              <View style={topNavbarStyles.spacer as ViewStyle} />
            )}
          </View>
        </View>
      </SafeAreaView>
    </>
  );
};

// Export default for easier imports
export default TopNavbar;
