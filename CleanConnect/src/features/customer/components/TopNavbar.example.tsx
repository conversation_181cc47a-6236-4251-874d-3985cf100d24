import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from 'react-native';
import { TopNavbar } from './TopNavbar';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { textStyles } from '@/constants/typography';

/**
 * Example component demonstrating various TopNavbar configurations
 * This shows different use cases and customization options
 */
export const TopNavbarExample: React.FC = () => {
  const [currentExample, setCurrentExample] = useState(0);

  const examples = [
    {
      title: 'Basic Navbar',
      description: 'Simple navbar with just a title',
      props: {
        title: 'Home',
      },
    },
    {
      title: 'With Back Button',
      description: 'Navbar with back navigation',
      props: {
        title: 'Service Details',
        showBack: true,
        onBackPress: () => Alert.alert('Back Pressed', 'Custom back handler'),
      },
    },
    {
      title: 'With Notifications',
      description: 'Navbar with notification icon and badge',
      props: {
        title: 'Notifications',
        showBack: true,
        showNotification: true,
      },
    },
    {
      title: 'With Cart',
      description: 'Navbar with shopping cart icon',
      props: {
        title: 'Services',
        showBack: true,
        showCart: true,
      },
    },
    {
      title: 'Full Featured',
      description: 'Navbar with all features enabled',
      props: {
        title: 'Booking Details',
        showBack: true,
        showNotification: true,
        showCart: true,
      },
    },
    {
      title: 'Custom Colors',
      description: 'Navbar with custom background and text colors',
      props: {
        title: 'Custom Theme',
        showBack: true,
        showNotification: true,
        showCart: true,
        backgroundColor: colors.primary,
        titleColor: colors.white,
        iconColor: colors.white,
      },
    },
    {
      title: 'Long Title',
      description: 'Navbar with a very long title that gets truncated',
      props: {
        title: 'This is a very long title that should be truncated properly',
        showBack: true,
        showNotification: true,
        showCart: true,
      },
    },
  ];

  const currentProps = examples[currentExample]?.props || examples[0].props;

  const nextExample = () => {
    setCurrentExample((prev) => (prev + 1) % examples.length);
  };

  const prevExample = () => {
    setCurrentExample((prev) => (prev - 1 + examples.length) % examples.length);
  };

  return (
    <View style={styles.container}>
      {/* Current Example Navbar */}
      <TopNavbar {...currentProps} />

      {/* Example Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.exampleInfo}>
          <Text style={styles.exampleTitle}>
            {examples[currentExample]?.title}
          </Text>
          <Text style={styles.exampleDescription}>
            {examples[currentExample]?.description}
          </Text>
        </View>

        {/* Navigation Controls */}
        <View style={styles.controls}>
          <TouchableOpacity style={styles.controlButton} onPress={prevExample}>
            <Text style={styles.controlButtonText}>← Previous</Text>
          </TouchableOpacity>
          
          <Text style={styles.counter}>
            {currentExample + 1} of {examples.length}
          </Text>
          
          <TouchableOpacity style={styles.controlButton} onPress={nextExample}>
            <Text style={styles.controlButtonText}>Next →</Text>
          </TouchableOpacity>
        </View>

        {/* Current Props Display */}
        <View style={styles.propsContainer}>
          <Text style={styles.propsTitle}>Current Props:</Text>
          <View style={styles.propsCode}>
            <Text style={styles.codeText}>
              {`<TopNavbar\n${Object.entries(currentProps)
                .map(([key, value]) => {
                  if (typeof value === 'function') {
                    return `  ${key}={() => { /* custom handler */ }}`;
                  }
                  if (typeof value === 'string') {
                    return `  ${key}="${value}"`;
                  }
                  return `  ${key}={${value}}`;
                })
                .join('\n')}\n/>`}
            </Text>
          </View>
        </View>

        {/* All Examples List */}
        <View style={styles.examplesList}>
          <Text style={styles.listTitle}>All Examples:</Text>
          {examples.map((example, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.exampleItem,
                index === currentExample && styles.exampleItemActive,
              ]}
              onPress={() => setCurrentExample(index)}
            >
              <Text
                style={[
                  styles.exampleItemTitle,
                  index === currentExample && styles.exampleItemTitleActive,
                ]}
              >
                {example.title}
              </Text>
              <Text
                style={[
                  styles.exampleItemDescription,
                  index === currentExample && styles.exampleItemDescriptionActive,
                ]}
              >
                {example.description}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Usage Instructions */}
        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>📋 Usage Instructions</Text>
          <Text style={styles.instructionText}>
            1. Import the TopNavbar component in your screen
          </Text>
          <Text style={styles.instructionText}>
            2. Add it at the top of your screen layout
          </Text>
          <Text style={styles.instructionText}>
            3. Configure props based on your screen needs
          </Text>
          <Text style={styles.instructionText}>
            4. The navbar will automatically handle notifications and cart navigation
          </Text>
          <Text style={styles.instructionText}>
            5. Use custom onBackPress for special back navigation logic
          </Text>
        </View>

        {/* Features List */}
        <View style={styles.features}>
          <Text style={styles.featuresTitle}>✨ Features</Text>
          <Text style={styles.featureText}>• Sticky positioning when scrolling</Text>
          <Text style={styles.featureText}>• Automatic notification badge display</Text>
          <Text style={styles.featureText}>• Responsive design for different screen sizes</Text>
          <Text style={styles.featureText}>• Customizable colors and styling</Text>
          <Text style={styles.featureText}>• Accessibility support with proper hit targets</Text>
          <Text style={styles.featureText}>• Consistent with app design system</Text>
          <Text style={styles.featureText}>• Safe area handling for different devices</Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  content: {
    flex: 1,
    padding: spacing.layout.container,
  },

  exampleInfo: {
    backgroundColor: colors.primary + '10',
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.xl,
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },

  exampleTitle: {
    ...textStyles.h3,
    color: colors.primary,
    marginBottom: spacing.sm,
  },

  exampleDescription: {
    ...textStyles.body,
    color: colors.text.secondary,
  },

  controls: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xl,
  },

  controlButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },

  controlButtonText: {
    ...textStyles.button,
    color: colors.white,
  },

  counter: {
    ...textStyles.labelLarge,
    color: colors.text.secondary,
  },

  propsContainer: {
    marginBottom: spacing.xl,
  },

  propsTitle: {
    ...textStyles.h4,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },

  propsCode: {
    backgroundColor: colors.gray[100],
    padding: spacing.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: colors.border.light,
  },

  codeText: {
    fontFamily: 'monospace',
    fontSize: 12,
    color: colors.text.primary,
    lineHeight: 18,
  },

  examplesList: {
    marginBottom: spacing.xl,
  },

  listTitle: {
    ...textStyles.h4,
    color: colors.text.primary,
    marginBottom: spacing.md,
  },

  exampleItem: {
    backgroundColor: colors.white,
    padding: spacing.lg,
    borderRadius: 8,
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border.light,
  },

  exampleItemActive: {
    backgroundColor: colors.primary + '10',
    borderColor: colors.primary,
  },

  exampleItemTitle: {
    ...textStyles.labelLarge,
    color: colors.text.primary,
    marginBottom: spacing.xs,
  },

  exampleItemTitleActive: {
    color: colors.primary,
  },

  exampleItemDescription: {
    ...textStyles.body,
    color: colors.text.secondary,
  },

  exampleItemDescriptionActive: {
    color: colors.text.primary,
  },

  instructions: {
    backgroundColor: colors.success + '10',
    padding: spacing.lg,
    borderRadius: 12,
    marginBottom: spacing.lg,
    borderWidth: 1,
    borderColor: colors.success + '20',
  },

  instructionsTitle: {
    ...textStyles.labelLarge,
    color: colors.success,
    marginBottom: spacing.md,
  },

  instructionText: {
    ...textStyles.body,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },

  features: {
    backgroundColor: colors.accent + '10',
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.accent + '20',
  },

  featuresTitle: {
    ...textStyles.labelLarge,
    color: colors.accent,
    marginBottom: spacing.md,
  },

  featureText: {
    ...textStyles.body,
    color: colors.text.secondary,
    marginBottom: spacing.xs,
  },
});
