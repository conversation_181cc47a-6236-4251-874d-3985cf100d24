import { StyleSheet, Platform } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography, textStyles } from '@/constants/typography';

export const topNavbarStyles = StyleSheet.create({
  // Safe Area Container
  safeArea: {
    backgroundColor: colors.white,
    // Ensure the navbar stays at the top when scrolling
    zIndex: 1000,
    elevation: Platform.OS === 'android' ? 8 : 0,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },

  // Main Container
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    paddingHorizontal: spacing.layout.container,
    paddingVertical: spacing.md,
    minHeight: 56, // Standard navbar height
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },

  // Left Section: Back Button + Logo
  leftSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'flex-start',
  },

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },

  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },

  logoIcon: {
    fontSize: 20,
    marginRight: spacing.xs,
  },

  logoText: {
    ...textStyles.labelLarge,
    color: colors.primary,
    fontWeight: typography.weights.semibold,
    letterSpacing: -0.5,
  },

  // Center Section: Title
  centerSection: {
    flex: 2,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.sm,
  },

  title: {
    ...textStyles.h4,
    color: colors.text.primary,
    textAlign: 'center',
    fontWeight: typography.weights.semibold,
    letterSpacing: -0.3,
  },

  // Right Section: Action Icons
  rightSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
    gap: spacing.sm,
  },

  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },

  iconContainer: {
    position: 'relative',
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Notification Badge
  notificationBadge: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: colors.error,
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xs,
    borderWidth: 2,
    borderColor: colors.white,
  },

  notificationCount: {
    ...textStyles.caption,
    color: colors.white,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.bold,
    lineHeight: typography.sizes.xs,
  },

  // Spacer for layout consistency
  spacer: {
    width: 40,
    height: 40,
  },

  // Responsive adjustments for different screen sizes (handled in component logic)
  // Note: React Native doesn't support CSS media queries
  // Responsive behavior should be handled in component with Dimensions API

  // Sticky behavior styles (for use with ScrollView)
  sticky: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },

  // Alternative compact layout for smaller screens
  compact: {
    paddingVertical: spacing.sm,
    minHeight: 48,
  },

  compactTitle: {
    fontSize: typography.sizes.md,
  },

  compactActionButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
  },

  // Theme variations
  lightTheme: {
    backgroundColor: colors.white,
    borderBottomColor: colors.border.light,
  },

  darkTheme: {
    backgroundColor: colors.gray[900],
    borderBottomColor: colors.gray[700],
  },

  primaryTheme: {
    backgroundColor: colors.primary,
    borderBottomColor: colors.primaryDark,
  },

  transparentTheme: {
    backgroundColor: 'transparent',
    borderBottomWidth: 0,
    shadowOpacity: 0,
    elevation: 0,
  },

  // Animation states
  slideIn: {
    transform: [{ translateY: 0 }],
  },

  slideOut: {
    transform: [{ translateY: -100 }],
  },

  // Accessibility improvements
  accessibilityFocus: {
    borderWidth: 2,
    borderColor: colors.primary,
  },

  // Loading state
  loading: {
    opacity: 0.6,
  },

  // Error state
  error: {
    backgroundColor: colors.error + '10',
    borderBottomColor: colors.error,
  },

  // Success state
  success: {
    backgroundColor: colors.success + '10',
    borderBottomColor: colors.success,
  },
});
