import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useBookingStore } from '../../../../store/bookingStore';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { styles } from './styles';

const paymentMethods = [
  {
    id: 'mastercard',
    name: 'Master Card',
    icon: '💳',
    color: '#EB001B',
  },
  {
    id: 'bizumx',
    name: 'Bizumx',
    icon: '⚡',
    color: '#00D4AA',
  },
  {
    id: 'applepay',
    name: 'Apple Pay',
    icon: '🍎',
    color: '#000000',
  },
];

export function PaymentMethodScreen() {
  const {
    selectedService,
    subtotal,
    discountAmount,
    taxes,
    total,
    discountCode,
    paymentMethod,
    setDiscountCode,
    applyDiscount,
    setPaymentMethod,
  } = useBookingStore();

  const [localDiscountCode, setLocalDiscountCode] = useState(discountCode);
  const [showPaymentSummary, setShowPaymentSummary] = useState(false);

  const handleApplyDiscount = () => {
    if (localDiscountCode.toLowerCase() === 'save20') {
      applyDiscount(50);
      setDiscountCode(localDiscountCode);
    } else {
      applyDiscount(0);
      setDiscountCode('');
    }
  };

  const handlePayNow = () => {
    if (paymentMethod) {
      // Navigate to booking confirmation or success screen
      router.push('/customer/booking/confirmation');
    }
  };

  const togglePaymentSummary = () => {
    setShowPaymentSummary(!showPaymentSummary);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Payment Method</Text>
        <Text style={styles.stepIndicator}>Step 3 of 3</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Service Summary */}
        {selectedService && (
          <View style={styles.serviceCard}>
            <Image
              source={{
                uri: selectedService.image || 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=100&h=100&fit=crop',
              }}
              style={styles.serviceImage}
            />
            <View style={styles.serviceInfo}>
              <Text style={styles.serviceName}>
                Luxurious Home Cleaning Mani-Pedi Bliss Awaits
              </Text>
              <Text style={styles.servicePrice}>
                Price <Text style={styles.priceAmount}>${selectedService.price}.00</Text>
              </Text>
            </View>
          </View>
        )}

        {/* Map Section */}
        <View style={styles.mapContainer}>
          <View style={styles.mapPlaceholder}>
            <View style={styles.mapPin}>
              <Ionicons name="location" size={24} color="#FF6B35" />
            </View>
            <Text style={styles.mapText}>Map View</Text>
          </View>
        </View>

        {/* Location */}
        <View style={styles.locationSection}>
          <Text style={styles.locationAddress}>Carrer de la Creu, 16 Barcelona</Text>
          <TouchableOpacity>
            <Text style={styles.editLocation}>Edit location</Text>
          </TouchableOpacity>
        </View>

        {/* Discount Code */}
        <View style={styles.discountSection}>
          <Text style={styles.sectionTitle}>Discount code</Text>
          <View style={styles.discountContainer}>
            <TextInput
              style={styles.discountInput}
              placeholder="Enter Discount code"
              value={localDiscountCode}
              onChangeText={setLocalDiscountCode}
            />
            <TouchableOpacity style={styles.applyButton} onPress={handleApplyDiscount}>
              <Text style={styles.applyButtonText}>Apply</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Payment Summary */}
        <View style={styles.summarySection}>
          <TouchableOpacity style={styles.summaryHeader} onPress={togglePaymentSummary}>
            <Text style={styles.summaryTitle}>Payment Summary</Text>
            <View style={styles.summaryToggle}>
              <Ionicons 
                name={showPaymentSummary ? "chevron-up" : "chevron-down"} 
                size={20} 
                color="#666" 
              />
            </View>
          </TouchableOpacity>

          {showPaymentSummary && (
            <View style={styles.summaryDetails}>
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Subtotal</Text>
                <Text style={styles.summaryValue}>${subtotal.toFixed(2)}</Text>
              </View>
              {discountAmount > 0 && (
                <View style={styles.summaryRow}>
                  <Text style={styles.summaryLabel}>Discount</Text>
                  <Text style={styles.discountValue}>-${discountAmount}</Text>
                </View>
              )}
              <View style={styles.summaryRow}>
                <Text style={styles.summaryLabel}>Taxes & Other Free</Text>
                <Text style={styles.summaryValue}>${taxes.toFixed(2)}</Text>
              </View>
              <View style={[styles.summaryRow, styles.totalRow]}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalValue}>${total.toFixed(2)}</Text>
              </View>
            </View>
          )}
        </View>

        {/* Payment Methods */}
        <View style={styles.paymentMethodsSection}>
          <Text style={styles.sectionTitle}>Payment method</Text>
          <View style={styles.paymentMethodsList}>
            {paymentMethods.map((method) => (
              <TouchableOpacity
                key={method.id}
                style={[
                  styles.paymentMethodItem,
                  paymentMethod === method.id && styles.paymentMethodSelected,
                ]}
                onPress={() => setPaymentMethod(method.id as any)}
              >
                <View style={styles.paymentMethodLeft}>
                  <Text style={styles.paymentMethodIcon}>{method.icon}</Text>
                  <Text style={styles.paymentMethodName}>{method.name}</Text>
                </View>
                <View style={[
                  styles.radioButton,
                  paymentMethod === method.id && styles.radioButtonSelected,
                ]}>
                  {paymentMethod === method.id && (
                    <View style={styles.radioButtonInner} />
                  )}
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.spacer} />
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Start from</Text>
          <Text style={styles.priceAmount}>${total.toFixed(2)}</Text>
        </View>
        <TouchableOpacity 
          style={[
            styles.payButton,
            !paymentMethod && styles.payButtonDisabled,
          ]} 
          onPress={handlePayNow}
          disabled={!paymentMethod}
        >
          <Text style={styles.payButtonText}>Pay Now</Text>
        </TouchableOpacity>
      </View>

      <CustomerNavigation />
    </View>
  );
}
