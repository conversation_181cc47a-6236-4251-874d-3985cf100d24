import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  } as ViewStyle,

  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  stepIndicator: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
  } as ViewStyle,

  serviceCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.lg,
  } as ViewStyle,

  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginRight: spacing.md,
  } as ViewStyle,

  serviceInfo: {
    flex: 1,
  } as ViewStyle,

  serviceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.sm,
    lineHeight: typography.lineHeights.tight,
  } as TextStyle,

  servicePrice: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  priceAmount: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  mapContainer: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    height: 200,
  } as ViewStyle,

  mapPlaceholder: {
    flex: 1,
    backgroundColor: colors.gray[100],
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  } as ViewStyle,

  mapPin: {
    position: 'absolute',
    top: '40%',
    left: '50%',
    transform: [{ translateX: -12 }, { translateY: -12 }],
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 8,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  } as ViewStyle,

  mapText: {
    fontSize: typography.sizes.md,
    color: colors.gray[500],
    fontFamily: typography.fontFamily.regular,
    marginTop: 40,
  } as TextStyle,

  locationSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  } as ViewStyle,

  locationAddress: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.xs,
  } as TextStyle,

  editLocation: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
    fontWeight: typography.weights.medium,
  } as TextStyle,

  discountSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  } as ViewStyle,

  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
  } as TextStyle,

  discountContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
  } as ViewStyle,

  discountInput: {
    flex: 1,
    backgroundColor: colors.gray[50],
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[900],
    borderWidth: 1,
    borderColor: colors.gray[200],
  } as ViewStyle,

  applyButton: {
    backgroundColor: colors.gray[400],
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  applyButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  summarySection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  } as ViewStyle,

  summaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  summaryTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  summaryToggle: {
    padding: spacing.xs,
  } as ViewStyle,

  summaryDetails: {
    marginTop: spacing.md,
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  } as ViewStyle,

  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  } as ViewStyle,

  summaryLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  summaryValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  discountValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  totalRow: {
    marginTop: spacing.sm,
    paddingTop: spacing.sm,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  } as ViewStyle,

  totalLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  totalValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  paymentMethodsSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
  } as ViewStyle,

  paymentMethodsList: {
    gap: spacing.md,
  } as ViewStyle,

  paymentMethodItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 12,
    backgroundColor: colors.gray[50],
  } as ViewStyle,

  paymentMethodSelected: {
    backgroundColor: colors.primary + '10',
    borderWidth: 1,
    borderColor: colors.primary,
  } as ViewStyle,

  paymentMethodLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.md,
  } as ViewStyle,

  paymentMethodIcon: {
    fontSize: 24,
  } as TextStyle,

  paymentMethodName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  radioButton: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: colors.gray[300],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  radioButtonSelected: {
    borderColor: colors.primary,
  } as ViewStyle,

  radioButtonInner: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: colors.primary,
  } as ViewStyle,

  spacer: {
    height: 100,
  } as ViewStyle,

  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  priceContainer: {
    flex: 1,
  } as ViewStyle,

  priceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  } as TextStyle,

  payButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 16,
    minWidth: 120,
    alignItems: 'center',
  } as ViewStyle,

  payButtonDisabled: {
    backgroundColor: colors.gray[300],
    opacity: 0.6,
  } as ViewStyle,

  payButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
});
