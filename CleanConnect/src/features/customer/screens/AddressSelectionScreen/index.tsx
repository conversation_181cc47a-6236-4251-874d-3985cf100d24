import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../../../components/common/Button';
import { Input } from '../../../../components/common/Input';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { addressSelectionScreenStyles } from '../../styles/AddressSelectionScreen.styles';

// Mock saved addresses
const savedAddresses = [
  {
    id: '1',
    label: 'Home',
    address1: '123 Main Street',
    address2: 'Apt 4B',
    city: 'Banjul',
    region: 'Greater Banjul',
    country: 'The Gambia',
    isPrimary: true,
  },
  {
    id: '2',
    label: 'Office',
    address1: '456 Business Avenue',
    address2: 'Floor 2',
    city: 'Banjul',
    region: 'Greater Banjul',
    country: 'The Gambia',
    isPrimary: false,
  },
];

export const AddressSelectionScreen: React.FC = () => {
  const [selectedAddress, setSelectedAddress] = useState<string | null>(null);
  const [showAddNew, setShowAddNew] = useState(false);
  const [newAddress, setNewAddress] = useState({
    label: '',
    address1: '',
    address2: '',
    city: '',
    region: '',
    country: 'The Gambia',
  });

  const handleAddressSelect = (addressId: string) => {
    setSelectedAddress(addressId);
    setShowAddNew(false);
  };

  const handleAddNewAddress = () => {
    setShowAddNew(true);
    setSelectedAddress(null);
  };

  const handleSaveNewAddress = () => {
    // Validate required fields
    if (!newAddress.label || !newAddress.address1 || !newAddress.city) {
      Alert.alert('Error', 'Please fill in all required fields');
      return;
    }

    // In a real app, you would save this to the database
    console.log('New address:', newAddress);
    setShowAddNew(false);
    setSelectedAddress('new');
    
    // Reset form
    setNewAddress({
      label: '',
      address1: '',
      address2: '',
      city: '',
      region: '',
      country: 'The Gambia',
    });
  };

  const handleContinue = () => {
    if (selectedAddress) {
      console.log('Selected address:', selectedAddress);
      // Navigate to provider selection
      // router.push('/customer/booking/provider');
    }
  };

  const getSelectedAddressData = () => {
    if (selectedAddress === 'new') {
      return newAddress;
    }
    return savedAddresses.find(addr => addr.id === selectedAddress);
  };

  return (
    <SafeAreaView style={addressSelectionScreenStyles.container}>
      {/* Header with Back Arrow */}
      <View style={addressSelectionScreenStyles.header}>
        <TouchableOpacity
          style={addressSelectionScreenStyles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <View style={addressSelectionScreenStyles.headerContent}>
          <Text style={addressSelectionScreenStyles.headerTitle}>Select Address</Text>
          <Text style={addressSelectionScreenStyles.headerSubtitle}>Choose where you&apos;d like the service</Text>
        </View>
      </View>

      <ScrollView style={addressSelectionScreenStyles.content} showsVerticalScrollIndicator={false}>
        {/* Saved Addresses */}
        <View style={addressSelectionScreenStyles.section}>
          <Text style={addressSelectionScreenStyles.sectionTitle}>Saved Addresses</Text>
          {savedAddresses.map((address) => (
            <TouchableOpacity
              key={address.id}
              style={[
                addressSelectionScreenStyles.addressCard,
                selectedAddress === address.id && addressSelectionScreenStyles.addressCardSelected,
              ]}
              onPress={() => handleAddressSelect(address.id)}
            >
              <View style={addressSelectionScreenStyles.addressHeader}>
                <View style={addressSelectionScreenStyles.addressLabelContainer}>
                  <Text style={addressSelectionScreenStyles.addressLabel}>{address.label}</Text>
                  {address.isPrimary && (
                    <View style={addressSelectionScreenStyles.primaryBadge}>
                      <Text style={addressSelectionScreenStyles.primaryText}>Primary</Text>
                    </View>
                  )}
                </View>
                <View>
                  <Text style={addressSelectionScreenStyles.addressIcon}>📍</Text>
                </View>
              </View>
              <Text style={addressSelectionScreenStyles.addressText}>
                {address.address1}
                {address.address2 && `, ${address.address2}`}
              </Text>
              <Text style={addressSelectionScreenStyles.addressText}>
                {address.city}, {address.region}
              </Text>
              <Text style={addressSelectionScreenStyles.addressText}>{address.country}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Add New Address */}
        <View style={addressSelectionScreenStyles.section}>
          <TouchableOpacity
            style={[
              addressSelectionScreenStyles.addAddressCard,
              showAddNew && addressSelectionScreenStyles.addAddressCardSelected,
            ]}
            onPress={handleAddNewAddress}
          >
            <Text style={addressSelectionScreenStyles.addAddressIcon}>➕</Text>
            <Text style={addressSelectionScreenStyles.addAddressText}>Add New Address</Text>
          </TouchableOpacity>
        </View>

        {/* New Address Form */}
        {showAddNew && (
          <View style={addressSelectionScreenStyles.section}>
            <Text style={addressSelectionScreenStyles.sectionTitle}>Add New Address</Text>
            <View style={addressSelectionScreenStyles.form}>
              <Input
                label="Address Label"
                placeholder="e.g., Home, Office, Vacation"
                value={newAddress.label}
                onChangeText={(text) => setNewAddress({ ...newAddress, label: text })}
              />
              <Input
                label="Street Address"
                placeholder="Enter street address"
                value={newAddress.address1}
                onChangeText={(text) => setNewAddress({ ...newAddress, address1: text })}
              />
              <Input
                label="Apartment, suite, etc. (optional)"
                placeholder="Apt, suite, floor, etc."
                value={newAddress.address2}
                onChangeText={(text) => setNewAddress({ ...newAddress, address2: text })}
              />
              <Input
                label="City"
                placeholder="Enter city"
                value={newAddress.city}
                onChangeText={(text) => setNewAddress({ ...newAddress, city: text })}
              />
              <Input
                label="Region/State"
                placeholder="Enter region or state"
                value={newAddress.region}
                onChangeText={(text) => setNewAddress({ ...newAddress, region: text })}
              />
              <Input
                label="Country"
                placeholder="Enter country"
                value={newAddress.country}
                onChangeText={(text) => setNewAddress({ ...newAddress, country: text })}
              />
              <Button
                title="Save Address"
                onPress={handleSaveNewAddress}
                style={addressSelectionScreenStyles.saveButton}
              />
            </View>
          </View>
        )}

        {/* Service Summary */}
        <View style={addressSelectionScreenStyles.section}>
          <Text style={addressSelectionScreenStyles.sectionTitle}>Service Summary</Text>
          <View style={addressSelectionScreenStyles.summaryCard}>
            <View style={addressSelectionScreenStyles.summaryRow}>
              <Text style={addressSelectionScreenStyles.summaryLabel}>Service:</Text>
              <Text style={addressSelectionScreenStyles.summaryValue}>House Cleaning</Text>
            </View>
            <View style={addressSelectionScreenStyles.summaryRow}>
              <Text style={addressSelectionScreenStyles.summaryLabel}>Date & Time:</Text>
              <Text style={addressSelectionScreenStyles.summaryValue}>Tomorrow, 10:00 AM</Text>
            </View>
            <View style={addressSelectionScreenStyles.summaryRow}>
              <Text style={addressSelectionScreenStyles.summaryLabel}>Price:</Text>
              <Text style={addressSelectionScreenStyles.summaryValue}>$50</Text>
            </View>
            {selectedAddress && (
              <View style={addressSelectionScreenStyles.summaryRow}>
                <Text style={addressSelectionScreenStyles.summaryLabel}>Address:</Text>
                <Text style={addressSelectionScreenStyles.summaryValue}>
                  {getSelectedAddressData()?.label || 'New Address'}
                </Text>
              </View>
            )}
          </View>
        </View>
      </ScrollView>

      {selectedAddress && (
        <View style={addressSelectionScreenStyles.footer}>
          <Button
            title="Continue"
            onPress={handleContinue}
            style={addressSelectionScreenStyles.continueButton}
          />
        </View>
      )}

      <CustomerNavigation />
    </SafeAreaView>
  );
};
