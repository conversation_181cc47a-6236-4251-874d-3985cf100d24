import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../../../components/common/Button';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { useBookingStore } from '../../../../store/useBookingStore';
import { bookingConfirmationScreenStyles } from '../../styles/BookingConfirmationScreen.styles';

export const BookingConfirmationScreen: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const { addBooking } = useBookingStore();

  // Mock booking data (in real app, this would come from previous screens)
  const bookingData = {
    service: {
      name: 'House Cleaning',
      price: 50,
      duration: '2-3 hours',
    },
    date: 'Tomorrow',
    time: '10:00 AM',
    address: {
      label: 'Home',
      fullAddress: '123 Main Street, Apt 4B, Banjul, Greater Banjul, The Gambia',
    },
    provider: null, // Will be auto-assigned
  };

  const handleConfirmBooking = async () => {
    setLoading(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create booking object
      const newBooking = {
        id: `booking-${Date.now()}`,
        customerId: 'user-123',
        serviceId: '1',
        addressId: '1',
        scheduledAt: new Date(),
        status: 'PENDING' as const,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Add to store
      addBooking(newBooking);
      
      // Show success message
      Alert.alert(
        'Booking Confirmed!',
        'Your booking has been successfully created. We will notify you when a provider accepts your request.',
        [
          {
            text: 'OK',
            onPress: () => {
              // Navigate to booking tracking screen
              console.log('Navigate to booking tracking');
            },
          },
        ]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create booking. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditBooking = (section: string) => {
    console.log('Edit section:', section);
    // Navigate back to respective screen
  };

  return (
    <SafeAreaView style={bookingConfirmationScreenStyles.container}>
      {/* Header with Back Arrow */}
      <View style={bookingConfirmationScreenStyles.header}>
        <TouchableOpacity
          style={bookingConfirmationScreenStyles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <View style={bookingConfirmationScreenStyles.headerContent}>
          <Text style={bookingConfirmationScreenStyles.headerTitle}>Confirm Booking</Text>
          <Text style={bookingConfirmationScreenStyles.headerSubtitle}>Review your booking details</Text>
        </View>
      </View>

      <ScrollView style={bookingConfirmationScreenStyles.content} showsVerticalScrollIndicator={false}>
        {/* Service Details */}
        <View style={bookingConfirmationScreenStyles.section}>
          <View style={bookingConfirmationScreenStyles.sectionHeader}>
            <Text style={bookingConfirmationScreenStyles.sectionTitle}>Service Details</Text>
            <TouchableOpacity onPress={() => handleEditBooking('service')}>
              <Text style={bookingConfirmationScreenStyles.editLink}>Edit</Text>
            </TouchableOpacity>
          </View>
          <View style={bookingConfirmationScreenStyles.serviceCard}>
            <View style={bookingConfirmationScreenStyles.serviceHeader}>
              <Text style={bookingConfirmationScreenStyles.serviceIcon}>🏠</Text>
              <View style={bookingConfirmationScreenStyles.serviceInfo}>
                <Text style={bookingConfirmationScreenStyles.serviceName}>{bookingData.service.name}</Text>
                <Text style={bookingConfirmationScreenStyles.serviceDuration}>{bookingData.service.duration}</Text>
              </View>
              <Text style={bookingConfirmationScreenStyles.servicePrice}>${bookingData.service.price}</Text>
            </View>
          </View>
        </View>

        {/* Date & Time */}
        <View style={bookingConfirmationScreenStyles.section}>
          <View style={bookingConfirmationScreenStyles.sectionHeader}>
            <Text style={bookingConfirmationScreenStyles.sectionTitle}>Date & Time</Text>
            <TouchableOpacity onPress={() => handleEditBooking('datetime')}>
              <Text style={bookingConfirmationScreenStyles.editLink}>Edit</Text>
            </TouchableOpacity>
          </View>
          <View style={bookingConfirmationScreenStyles.detailCard}>
            <View style={bookingConfirmationScreenStyles.detailRow}>
              <Text style={bookingConfirmationScreenStyles.detailIcon}>📅</Text>
              <Text style={bookingConfirmationScreenStyles.detailText}>{bookingData.date}</Text>
            </View>
            <View style={bookingConfirmationScreenStyles.detailRow}>
              <Text style={bookingConfirmationScreenStyles.detailIcon}>⏰</Text>
              <Text style={bookingConfirmationScreenStyles.detailText}>{bookingData.time}</Text>
            </View>
          </View>
        </View>

        {/* Address */}
        <View style={bookingConfirmationScreenStyles.section}>
          <View style={bookingConfirmationScreenStyles.sectionHeader}>
            <Text style={bookingConfirmationScreenStyles.sectionTitle}>Service Address</Text>
            <TouchableOpacity onPress={() => handleEditBooking('address')}>
              <Text style={bookingConfirmationScreenStyles.editLink}>Edit</Text>
            </TouchableOpacity>
          </View>
          <View style={bookingConfirmationScreenStyles.detailCard}>
            <View style={bookingConfirmationScreenStyles.detailRow}>
              <Text style={bookingConfirmationScreenStyles.detailIcon}>📍</Text>
              <View style={bookingConfirmationScreenStyles.addressInfo}>
                <Text style={bookingConfirmationScreenStyles.addressLabel}>{bookingData.address.label}</Text>
                <Text style={bookingConfirmationScreenStyles.addressText}>{bookingData.address.fullAddress}</Text>
              </View>
            </View>
          </View>
        </View>

        {/* Provider Assignment */}
        <View style={bookingConfirmationScreenStyles.section}>
          <Text style={bookingConfirmationScreenStyles.sectionTitle}>Provider Assignment</Text>
          <View style={bookingConfirmationScreenStyles.detailCard}>
            <View style={bookingConfirmationScreenStyles.detailRow}>
              <Text style={bookingConfirmationScreenStyles.detailIcon}>👤</Text>
              <View style={bookingConfirmationScreenStyles.providerInfo}>
                <Text style={bookingConfirmationScreenStyles.providerStatus}>Auto-assign</Text>
                <Text style={bookingConfirmationScreenStyles.providerDescription}>
                  We&apos;ll automatically assign the best available provider for your service
                </Text>
              </View>
            </View>
          </View>
        </View>

        {/* Pricing Breakdown */}
        <View style={bookingConfirmationScreenStyles.section}>
          <Text style={bookingConfirmationScreenStyles.sectionTitle}>Pricing</Text>
          <View style={bookingConfirmationScreenStyles.pricingCard}>
            <View style={bookingConfirmationScreenStyles.pricingRow}>
              <Text style={bookingConfirmationScreenStyles.pricingLabel}>Service Fee</Text>
              <Text style={bookingConfirmationScreenStyles.pricingValue}>${bookingData.service.price}</Text>
            </View>
            <View style={bookingConfirmationScreenStyles.pricingRow}>
              <Text style={bookingConfirmationScreenStyles.pricingLabel}>Platform Fee</Text>
              <Text style={bookingConfirmationScreenStyles.pricingValue}>$5</Text>
            </View>
            <View style={bookingConfirmationScreenStyles.pricingDivider} />
            <View style={bookingConfirmationScreenStyles.pricingRow}>
              <Text style={bookingConfirmationScreenStyles.totalLabel}>Total</Text>
              <Text style={bookingConfirmationScreenStyles.totalValue}>${bookingData.service.price + 5}</Text>
            </View>
          </View>
        </View>

        {/* Terms & Conditions */}
        <View style={bookingConfirmationScreenStyles.section}>
          <Text style={bookingConfirmationScreenStyles.sectionTitle}>Terms & Conditions</Text>
          <View style={bookingConfirmationScreenStyles.termsCard}>
            <Text style={bookingConfirmationScreenStyles.termsText}>
              • Cancellation is free up to 2 hours before the scheduled time{'\n'}
              • Payment will be processed after service completion{'\n'}
              • Provider will arrive within 15 minutes of scheduled time{'\n'}
              • You can rate and review the service after completion
            </Text>
          </View>
        </View>
      </ScrollView>

      <View style={bookingConfirmationScreenStyles.footer}>
        <View style={bookingConfirmationScreenStyles.totalSection}>
          <Text style={bookingConfirmationScreenStyles.totalText}>Total Amount</Text>
          <Text style={bookingConfirmationScreenStyles.totalAmount}>${bookingData.service.price + 5}</Text>
        </View>
        <Button
          title="Confirm Booking"
          onPress={handleConfirmBooking}
          loading={loading}
          style={bookingConfirmationScreenStyles.confirmButton}
        />
      </View>

      <CustomerNavigation />
    </SafeAreaView>
  );
};
