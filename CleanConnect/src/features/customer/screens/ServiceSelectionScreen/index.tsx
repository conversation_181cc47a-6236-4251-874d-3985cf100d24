import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { TopNavbar } from '../../components/TopNavbar';
import { serviceSelectionScreenStyles } from '../../styles/ServiceSelectionScreen.styles';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { useBookingStore } from '../../../../store/bookingStore';

// Services data matching the cleaning services design
const services = [
  {
    id: '1',
    name: 'Total Home Cleaning',
    description: 'Complete house cleaning service',
    price: 120,
    icon: '🏠',
    duration: '3-4 hours',
    popular: true,
    image: '🏠', // House icon
    backgroundColor: '#E3F2FD',
  },
  {
    id: '2',
    name: 'Restroom Cleaning',
    description: 'Bathroom deep cleaning',
    price: 45,
    icon: '🚿',
    duration: '1-2 hours',
    popular: false,
    image: '🚿', // Bathroom icon
    backgroundColor: '#F3E5F5',
  },
  {
    id: '3',
    name: 'Lounge Cleaning',
    description: 'Living room cleaning service',
    price: 65,
    icon: '🛋️',
    duration: '2-3 hours',
    popular: false,
    image: '🛋️', // Sofa icon
    backgroundColor: '#FFF3E0',
  },
  {
    id: '4',
    name: 'Cooking Space Cleaning',
    description: 'Kitchen deep cleaning',
    price: 75,
    icon: '🍳',
    duration: '2-3 hours',
    popular: false,
    image: '🍳', // Kitchen icon
    backgroundColor: '#E8F5E8',
  },
  {
    id: '5',
    name: 'Roof Drain Cleaning',
    description: 'Gutter and drain cleaning',
    price: 85,
    icon: '🏠',
    duration: '2-4 hours',
    popular: false,
    image: '🔧', // Maintenance icon
    backgroundColor: '#FFF8E1',
  },
  {
    id: '6',
    name: 'Glass Cleaning',
    description: 'Window and glass cleaning',
    price: 55,
    icon: '🪟',
    duration: '1-2 hours',
    popular: false,
    image: '🪟', // Window icon
    backgroundColor: '#E1F5FE',
  },
  {
    id: '7',
    name: 'One-Day Home Cleaning',
    description: 'Quick home cleaning service',
    price: 95,
    icon: '🏠',
    duration: '4-6 hours',
    popular: false,
    image: '🧹', // Cleaning icon
    backgroundColor: '#FCE4EC',
  },
  {
    id: '8',
    name: 'Surface Buffing & Cleaning',
    description: 'Floor and surface polishing',
    price: 70,
    icon: '✨',
    duration: '2-3 hours',
    popular: false,
    image: '✨', // Sparkle icon
    backgroundColor: '#FFEBEE',
  },
];

export const ServiceSelectionScreen: React.FC = () => {
  const { selectedService, setSelectedService } = useBookingStore();
  const [localSelectedService, setLocalSelectedService] = useState<string | null>(selectedService?.id || '1'); // Default to first service

  const handleServiceSelect = (serviceId: string) => {
    setLocalSelectedService(serviceId);
    const service = services.find(s => s.id === serviceId);
    if (service) {
      setSelectedService({
        id: service.id,
        name: service.name,
        icon: service.icon,
        price: service.price,
        description: service.description,
      });
    }
  };

  const handleNext = () => {
    if (localSelectedService) {
      const service = services.find(s => s.id === localSelectedService);
      if (service) {
        router.push(`/customer/service-detail/${localSelectedService}`);
      }
    }
  };

  const renderServiceCard = (service: typeof services[0]) => {
    const isSelected = localSelectedService === service.id;

    return (
      <TouchableOpacity
        key={service.id}
        style={[
          serviceSelectionScreenStyles.serviceCardClean,
          isSelected && serviceSelectionScreenStyles.serviceCardCleanSelected,
        ]}
        onPress={() => handleServiceSelect(service.id)}
        activeOpacity={0.8}
      >
        <View style={[
          serviceSelectionScreenStyles.serviceImageContainerClean,
          { backgroundColor: service.backgroundColor },
          isSelected && serviceSelectionScreenStyles.serviceImageContainerCleanSelected,
        ]}>
          <Text style={serviceSelectionScreenStyles.serviceImageClean}>
            {service.image}
          </Text>
        </View>
        <Text style={serviceSelectionScreenStyles.serviceNameClean} numberOfLines={2}>
          {service.name}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={serviceSelectionScreenStyles.container}>
     

      {/* Header with title */}
      <View style={serviceSelectionScreenStyles.headerClean}>
        <Text style={serviceSelectionScreenStyles.headerTitleClean}>
          What type of cleaning{'\n'} do you need done?
        </Text>
      </View>

      {/* Services Grid */}
      <ScrollView
        style={serviceSelectionScreenStyles.content}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={serviceSelectionScreenStyles.scrollContent}
      >
        <View style={serviceSelectionScreenStyles.servicesGridClean}>
          {services.map((service) => renderServiceCard(service))}
        </View>
      </ScrollView>

      {/* Next Button */}
      <View style={serviceSelectionScreenStyles.nextButtonContainer}>
        <TouchableOpacity
          style={[
            serviceSelectionScreenStyles.nextButton,
            !localSelectedService && serviceSelectionScreenStyles.nextButtonDisabled,
          ]}
          onPress={handleNext}
          disabled={!localSelectedService}
          activeOpacity={0.8}
        >
          <Text style={[
            serviceSelectionScreenStyles.nextButtonText,
            !localSelectedService && serviceSelectionScreenStyles.nextButtonTextDisabled,
          ]}>
            Next
          </Text>
        </TouchableOpacity>
      </View>

      <CustomerNavigation />
    </View>
  );
};
