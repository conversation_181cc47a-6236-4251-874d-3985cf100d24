import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  } as ViewStyle,

  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  stepIndicator: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
  } as ViewStyle,

  providerSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    alignItems: 'center',
    marginBottom: spacing.xl,
  } as ViewStyle,

  providerHeader: {
    position: 'relative',
    marginBottom: spacing.md,
  } as ViewStyle,

  providerAvatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 4,
    borderColor: colors.white,
  } as ViewStyle,

  verificationBadge: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: 4,
  } as ViewStyle,

  providerName: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    textAlign: 'center',
    marginBottom: spacing.xs,
  } as TextStyle,

  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: spacing.md,
  } as ViewStyle,

  locationText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  providerDescription: {
    fontSize: typography.sizes.md,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: typography.lineHeights.relaxed,
  } as TextStyle,

  moreLink: {
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  statsContainer: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    justifyContent: 'space-around',
  } as ViewStyle,

  statItem: {
    alignItems: 'center',
  } as ViewStyle,

  statValue: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: 4,
  } as TextStyle,

  statLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  ratingSection: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    marginBottom: spacing.xl,
  } as ViewStyle,

  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.lg,
  } as TextStyle,

  ratingHeader: {
    alignItems: 'center',
    marginBottom: spacing.lg,
  } as ViewStyle,

  ratingBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.gray[50],
    borderRadius: 50,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  } as ViewStyle,

  laurelLeft: {
    marginRight: spacing.sm,
  } as ViewStyle,

  laurelRight: {
    marginLeft: spacing.sm,
    transform: [{ scaleX: -1 }],
  } as ViewStyle,

  laurelText: {
    fontSize: 20,
  } as TextStyle,

  ratingValue: {
    fontSize: 48,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  overallRating: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.lg,
  } as TextStyle,

  ratingBreakdown: {
    marginBottom: spacing.xl,
  } as ViewStyle,

  ratingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  } as ViewStyle,

  ratingStars: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.medium,
    width: 20,
    marginRight: spacing.md,
  } as TextStyle,

  ratingBar: {
    flex: 1,
    height: 8,
    backgroundColor: colors.gray[200],
    borderRadius: 4,
    overflow: 'hidden',
  } as ViewStyle,

  ratingFill: {
    height: '100%',
    backgroundColor: colors.gray[900],
    borderRadius: 4,
  } as ViewStyle,

  reviewsContainer: {
    gap: spacing.lg,
  } as ViewStyle,

  reviewItem: {
    paddingBottom: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  } as ViewStyle,

  reviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  } as ViewStyle,

  reviewAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: spacing.md,
  } as ViewStyle,

  reviewInfo: {
    flex: 1,
  } as ViewStyle,

  reviewerName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    marginBottom: 4,
  } as TextStyle,

  reviewStars: {
    flexDirection: 'row',
    gap: 2,
  } as ViewStyle,

  reviewComment: {
    fontSize: typography.sizes.md,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.regular,
    lineHeight: typography.lineHeights.relaxed,
  } as TextStyle,

  spacer: {
    height: 100,
  } as ViewStyle,

  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  priceContainer: {
    flex: 1,
  } as ViewStyle,

  priceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  } as TextStyle,

  priceAmount: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  setTimeButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 16,
    minWidth: 120,
    alignItems: 'center',
  } as ViewStyle,

  setTimeButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
});
