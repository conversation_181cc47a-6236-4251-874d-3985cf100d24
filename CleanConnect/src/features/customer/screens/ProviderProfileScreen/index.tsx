import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useBookingStore } from '../../../../store/bookingStore';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { styles } from './styles';

const mockProvider = {
  id: '1',
  name: '<PERSON><PERSON>',
  avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
  location: 'Barcelona, Spain',
  rating: 4.98,
  totalReviews: 584,
  totalHired: 767,
  experience: '5+ Years',
  description: 'Skilled spa specialist with over 5 years of experience in providing top-notch cleaning services. Specialized in residential and commercial cleaning with attention to detail.',
  isPremium: true,
  isBasic: false,
  reviews: [
    {
      id: '1',
      userName: '<PERSON>',
      userAvatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face',
      rating: 5,
      comment: 'Excellent service! Very professional and thorough cleaning.',
      date: '2024-11-15',
    },
    {
      id: '2',
      userName: 'Devon Lane',
      userAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face',
      rating: 5,
      comment: 'Amazing work! Will definitely book again.',
      date: '2024-11-10',
    },
  ],
};

const ratingBreakdown = [
  { stars: 5, percentage: 85 },
  { stars: 4, percentage: 10 },
  { stars: 3, percentage: 3 },
  { stars: 2, percentage: 1 },
  { stars: 1, percentage: 1 },
];

export function ProviderProfileScreen() {
  const { id } = useLocalSearchParams();
  const { total, setSelectedProvider } = useBookingStore();

  const handleSetTime = () => {
    setSelectedProvider(mockProvider);
    router.push('/customer/booking/confirm-review');
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Ionicons
        key={index}
        name={index < rating ? 'star' : 'star-outline'}
        size={16}
        color="#FFB800"
      />
    ));
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.stepIndicator}>Step 2 of 3</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Provider Info */}
        <View style={styles.providerSection}>
          <View style={styles.providerHeader}>
            <Image source={{ uri: mockProvider.avatar }} style={styles.providerAvatar} />
            <View style={styles.verificationBadge}>
              <Ionicons name="checkmark-circle" size={24} color="#4CAF50" />
            </View>
          </View>
          
          <Text style={styles.providerName}>{mockProvider.name}</Text>
          <View style={styles.locationContainer}>
            <Ionicons name="location-outline" size={16} color="#666" />
            <Text style={styles.locationText}>{mockProvider.location}</Text>
          </View>
          
          <Text style={styles.providerDescription}>
            {mockProvider.description}
            <Text style={styles.moreLink}> More</Text>
          </Text>
        </View>

        {/* Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockProvider.experience}</Text>
            <Text style={styles.statLabel}>experience</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockProvider.totalReviews}+</Text>
            <Text style={styles.statLabel}>Total Review</Text>
          </View>
          <View style={styles.statItem}>
            <Text style={styles.statValue}>{mockProvider.totalHired}+</Text>
            <Text style={styles.statLabel}>Total Hired</Text>
          </View>
        </View>

        {/* Rating Section */}
        <View style={styles.ratingSection}>
          <Text style={styles.sectionTitle}>Rating & Review</Text>
          
          <View style={styles.ratingHeader}>
            <View style={styles.ratingBadge}>
              <View style={styles.laurelLeft}>
                <Text style={styles.laurelText}>🌿</Text>
              </View>
              <Text style={styles.ratingValue}>{mockProvider.rating}</Text>
              <View style={styles.laurelRight}>
                <Text style={styles.laurelText}>🌿</Text>
              </View>
            </View>
          </View>

          <Text style={styles.overallRating}>
            Overall Rating ({mockProvider.totalReviews} review)
          </Text>

          {/* Rating Breakdown */}
          <View style={styles.ratingBreakdown}>
            {ratingBreakdown.map((item) => (
              <View key={item.stars} style={styles.ratingRow}>
                <Text style={styles.ratingStars}>{item.stars}</Text>
                <View style={styles.ratingBar}>
                  <View 
                    style={[styles.ratingFill, { width: `${item.percentage}%` }]} 
                  />
                </View>
              </View>
            ))}
          </View>

          {/* Reviews */}
          <View style={styles.reviewsContainer}>
            {mockProvider.reviews.map((review) => (
              <View key={review.id} style={styles.reviewItem}>
                <View style={styles.reviewHeader}>
                  <Image source={{ uri: review.userAvatar }} style={styles.reviewAvatar} />
                  <View style={styles.reviewInfo}>
                    <Text style={styles.reviewerName}>{review.userName}</Text>
                    <View style={styles.reviewStars}>
                      {renderStars(review.rating)}
                    </View>
                  </View>
                </View>
                <Text style={styles.reviewComment}>{review.comment}</Text>
              </View>
            ))}
          </View>
        </View>

        <View style={styles.spacer} />
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Start from</Text>
          <Text style={styles.priceAmount}>${total.toFixed(2)}</Text>
        </View>
        <TouchableOpacity style={styles.setTimeButton} onPress={handleSetTime}>
          <Text style={styles.setTimeButtonText}>Set Time</Text>
        </TouchableOpacity>
      </View>

      <CustomerNavigation />
    </View>
  );
}
