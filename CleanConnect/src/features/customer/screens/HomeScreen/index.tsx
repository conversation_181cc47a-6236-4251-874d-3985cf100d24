import React, { useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { useNotificationStore } from '../../../../store/useNotificationStore';
import { homeScreenStyles } from '../../styles/HomeScreen.styles';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { serviceCategories as serviceCategoriesData } from '../../../../data/serviceCategories';

// Use dynamic service categories from data
const serviceCategories = serviceCategoriesData.map(category => ({
  id: category.id,
  name: category.name,
  icon: category.icon,
  color: category.color,
}));

// Most booked packages inspired by the images
const mostBookedPackages = [
  {
    id: '1',
    name: 'Tan removal facial',
    originalPrice: 1239,
    discountedPrice: 1199,
    image: '🧴',
    rating: 4.8,
  },
  {
    id: '2',
    name: 'Russian manicure',
    originalPrice: 500,
    discountedPrice: 400,
    image: '💅',
    rating: 4.9,
  },
  {
    id: '3',
    name: 'French pedicure',
    originalPrice: 649,
    discountedPrice: 629,
    image: '🦶',
    rating: 4.7,
  },
];

// Popular deals inspired by the images
const popularDeals = [
  {
    id: '1',
    name: 'Classic Bathroom Cleaning',
    price: 419,
    discountedPrice: 399,
    image: '🚿',
  },
  {
    id: '2',
    name: 'Two Deep Bathroom Cleaning',
    price: 1099,
    discountedPrice: 899,
    image: '🛁',
  },
  {
    id: '3',
    name: 'Bedroom Cleaning',
    price: 759,
    discountedPrice: 699,
    image: '🛏️',
  },
];

// Recommended packages
const recommendedPackages = [
  {
    id: '1',
    name: 'Jet Spray AC Service',
    price: 649,
    image: '❄️',
  },
  {
    id: '2',
    name: 'AC Installation',
    price: 749,
    originalPrice: 769,
    image: '🏠',
  },
  {
    id: '3',
    name: 'Foam Jet Split AC Service',
    price: 759,
    image: '💨',
  },
];



export const HomeScreen: React.FC = () => {
  const { unreadCount, subscribeToUserNotifications, simulateNewNotification } = useNotificationStore();

  useEffect(() => {
    // Subscribe to user notifications (mock for now)
    const userId = 'user-123'; // In real app, get from auth store
    subscribeToUserNotifications(userId, (notification) => {
      console.log('New notification received:', notification);
      // Handle notification (show toast, update UI, etc.)
    });

    // Simulate some notifications for demo
    setTimeout(() => {
      simulateNewNotification({
        id: '1',
        type: 'booking_update',
        title: 'Booking Confirmed',
        message: 'Your house cleaning booking has been confirmed by John Doe',
        read: false,
        createdAt: new Date().toISOString(),
      });
    }, 3000);

    return () => {
      // Cleanup subscriptions when component unmounts
      // This will be handled by the store
    };
  }, []);

  const handleCategoryPress = (category: typeof serviceCategories[0]) => {
    // Navigate to category-based service detail screen
    router.push(`/customer/category/${category.id}`);
  };

  const handlePackagePress = (packageItem: typeof mostBookedPackages[0]) => {
    // Navigate to package details
    router.push('/customer/booking/service-selection');
  };

  const handleDealPress = (deal: typeof popularDeals[0]) => {
    // Navigate to deal details or apply deal
    console.log('Deal selected:', deal.name);
  };

  const handleRecommendedPress = (item: typeof recommendedPackages[0]) => {
    // Navigate to recommended package details
    router.push('/customer/booking/service-selection');
  };

  const handleSearchPress = () => {
    // Navigate to search screen
    router.push('/customer/search');
  };

  const handleNotificationPress = () => {
    router.push('/customer/notifications');
  };

  const handleCartPress = () => {
    // Navigate to cart/orders screen
    router.push('/customer/bookings');
  };

  const handleProfilePress = () => {
    router.push('/customer/profile');
  };

  return (
    <SafeAreaView style={homeScreenStyles.container}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header with Logo and Actions */}
        <View style={homeScreenStyles.header}>
          <View style={homeScreenStyles.headerTop}>
            <View style={homeScreenStyles.logoContainer}>
              <View style={homeScreenStyles.logoWrapper}>
                <Text style={homeScreenStyles.logoText}>CleanConnect</Text>
              </View>
              <View style={homeScreenStyles.locationContainer}>
                <Text style={homeScreenStyles.locationLabel}>📍 Ms Jallow</Text>
                <Text style={homeScreenStyles.locationText}>2-24-30 Visakhapatnam</Text>
              </View>
            </View>
            <View style={homeScreenStyles.headerActions}>
              <TouchableOpacity
                style={homeScreenStyles.actionButton}
                onPress={handleNotificationPress}
              >
                <Text style={homeScreenStyles.actionIcon}>🔔</Text>
                {unreadCount > 0 && (
                  <View style={homeScreenStyles.notificationBadge}>
                    <Text style={homeScreenStyles.notificationCount}>{unreadCount}</Text>
                  </View>
                )}
              </TouchableOpacity>
              <TouchableOpacity
                style={homeScreenStyles.actionButton}
                onPress={handleCartPress}
              >
                <Text style={homeScreenStyles.actionIcon}>🛒</Text>
              </TouchableOpacity>
            </View>
          </View>

          {/* Search Bar */}
          <TouchableOpacity style={homeScreenStyles.searchContainer} onPress={handleSearchPress}>
            <Text style={homeScreenStyles.searchIcon}>🔍</Text>
            <Text style={homeScreenStyles.searchPlaceholder}>Search for Plumber</Text>
          </TouchableOpacity>

          {/* Promotional Banner */}
          <View style={homeScreenStyles.promoBanner}>
            <View style={homeScreenStyles.promoContent}>
              <Text style={homeScreenStyles.promoTitle}>Fresh & Clean Homes</Text>
              <Text style={homeScreenStyles.promoSubtitle}>Limited Time Discount!</Text>
              <Text style={homeScreenStyles.promoDescription}>Book now for a sparkling home!</Text>
            </View>
            <View style={homeScreenStyles.promoIcon}>
              <Text style={homeScreenStyles.promoEmoji}>✨</Text>
            </View>
          </View>
        </View>

        {/* Service Categories */}
        <View style={homeScreenStyles.section}>
          <View style={homeScreenStyles.categoriesGrid}>
            {serviceCategories.map((category) => (
              <TouchableOpacity
                key={category.id}
                style={homeScreenStyles.categoryCard}
                onPress={() => handleCategoryPress(category)}
              >
                <View style={[homeScreenStyles.categoryIconContainer, { backgroundColor: category.color }]}>
                  <Text style={homeScreenStyles.categoryIcon}>{category.icon}</Text>
                </View>
                <Text style={homeScreenStyles.categoryName}>{category.name}</Text>
              </TouchableOpacity>
            ))}
          </View>

          {/* Category Dots Indicator */}
          <View style={homeScreenStyles.dotsContainer}>
            <View style={[homeScreenStyles.dot, homeScreenStyles.activeDot]} />
            <View style={homeScreenStyles.dot} />
          </View>
        </View>

        {/* Membership Banner */}
        <View style={homeScreenStyles.section}>
          <View style={homeScreenStyles.membershipBanner}>
            <View style={homeScreenStyles.membershipContent}>
              <Text style={homeScreenStyles.membershipTitle}>BUY MEMBERSHIP</Text>
              <Text style={homeScreenStyles.membershipSubtitle}>Coupons, Discounts, Explore the</Text>
              <Text style={homeScreenStyles.membershipSubtitle}>exclusive benefits of our</Text>
              <Text style={homeScreenStyles.membershipSubtitle}>membership today!</Text>
              <TouchableOpacity style={homeScreenStyles.buyNowButton}>
                <Text style={homeScreenStyles.buyNowText}>BUY NOW</Text>
              </TouchableOpacity>
            </View>
            <View style={homeScreenStyles.membershipIcon}>
              <Text style={homeScreenStyles.crownEmoji}>👑</Text>
            </View>
          </View>
        </View>

        {/* Most Booked Packages */}
        <View style={homeScreenStyles.section}>
          <Text style={homeScreenStyles.sectionTitle}>Most Booked Packages</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false} style={homeScreenStyles.horizontalScroll}>
            {mostBookedPackages.map((packageItem) => (
              <TouchableOpacity
                key={packageItem.id}
                style={homeScreenStyles.packageCard}
                onPress={() => handlePackagePress(packageItem)}
              >
                <View style={homeScreenStyles.packageImageContainer}>
                  <Text style={homeScreenStyles.packageImage}>{packageItem.image}</Text>
                </View>
                <Text style={homeScreenStyles.packageName}>{packageItem.name}</Text>
                <View style={homeScreenStyles.packagePriceContainer}>
                  <Text style={homeScreenStyles.packagePrice}>₹{packageItem.discountedPrice}</Text>
                  <Text style={homeScreenStyles.packageOriginalPrice}>₹{packageItem.originalPrice}</Text>
                </View>
                <TouchableOpacity style={homeScreenStyles.addButton}>
                  <Text style={homeScreenStyles.addButtonText}>Add</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        {/* Popular Deals */}
        <View style={homeScreenStyles.section}>
          <Text style={homeScreenStyles.sectionTitle}>Popular Deals</Text>
          <View style={homeScreenStyles.dealsGrid}>
            {popularDeals.map((deal) => (
              <TouchableOpacity
                key={deal.id}
                style={homeScreenStyles.dealCard}
                onPress={() => handleDealPress(deal)}
              >
                <View style={homeScreenStyles.dealImageContainer}>
                  <Text style={homeScreenStyles.dealImage}>{deal.image}</Text>
                </View>
                <Text style={homeScreenStyles.dealName}>{deal.name}</Text>
                <Text style={homeScreenStyles.dealPrice}>₹{deal.discountedPrice}</Text>
                <TouchableOpacity style={homeScreenStyles.addButton}>
                  <Text style={homeScreenStyles.addButtonText}>Add</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Recommended Packages */}
        <View style={homeScreenStyles.section}>
          <Text style={homeScreenStyles.sectionTitle}>Recommended Packages</Text>
          <View style={homeScreenStyles.dealsGrid}>
            {recommendedPackages.map((item) => (
              <TouchableOpacity
                key={item.id}
                style={homeScreenStyles.dealCard}
                onPress={() => handleRecommendedPress(item)}
              >
                <View style={homeScreenStyles.dealImageContainer}>
                  <Text style={homeScreenStyles.dealImage}>{item.image}</Text>
                </View>
                <Text style={homeScreenStyles.dealName}>{item.name}</Text>
                <Text style={homeScreenStyles.dealPrice}>₹{item.price}</Text>
                {item.originalPrice && (
                  <Text style={homeScreenStyles.dealOriginalPrice}>₹{item.originalPrice}</Text>
                )}
                <TouchableOpacity style={homeScreenStyles.addButton}>
                  <Text style={homeScreenStyles.addButtonText}>Add</Text>
                </TouchableOpacity>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Bottom Spacing */}
        <View style={homeScreenStyles.bottomSpacing} />
      </ScrollView>

      <CustomerNavigation />
    </SafeAreaView>
  );
};