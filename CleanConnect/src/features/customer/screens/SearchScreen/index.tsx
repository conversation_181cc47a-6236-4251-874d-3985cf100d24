import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  FlatList,
} from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { textStyles } from '@/constants/typography';
import { ServiceCard } from '../../../../components/common/ServiceCard';
import { TopNavbar } from '../../components/TopNavbar';
import { searchScreenStyles } from '../../styles/SearchScreen.styles';

// Mock data for search results
const allServices = [
  {
    id: '1',
    name: 'Plumbing Service',
    description: 'Professional plumbing repairs and installations',
    price: 150,
    duration: '1-2 hours',
    icon: '🔧',
    rating: 4.8,
    reviewCount: 156,
    category: 'plumbing',
    features: ['Pipe repairs', 'Leak fixing', 'Installation'],
  },
  {
    id: '2',
    name: 'Electrical Service',
    description: 'Electrical repairs and wiring solutions',
    price: 120,
    duration: '1-3 hours',
    icon: '⚡',
    rating: 4.7,
    reviewCount: 89,
    category: 'electrical',
    features: ['Wiring', 'Switch repairs', 'Installation'],
  },
  {
    id: '3',
    name: 'House Cleaning',
    description: 'Complete home cleaning service',
    price: 80,
    duration: '2-3 hours',
    icon: '🏠',
    rating: 4.9,
    reviewCount: 234,
    category: 'cleaning',
    features: ['Deep cleaning', 'Kitchen', 'Bathroom'],
  },
  {
    id: '4',
    name: 'AC Service',
    description: 'Air conditioning maintenance and repair',
    price: 200,
    duration: '2-4 hours',
    icon: '❄️',
    rating: 4.6,
    reviewCount: 78,
    category: 'repair',
    features: ['Cleaning', 'Gas refill', 'Repair'],
  },
  {
    id: '5',
    name: 'Beauty Service',
    description: 'Professional beauty and grooming services',
    price: 100,
    duration: '1-2 hours',
    icon: '💄',
    rating: 4.8,
    reviewCount: 167,
    category: 'beauty',
    features: ['Facial', 'Manicure', 'Pedicure'],
  },
  {
    id: '6',
    name: 'Carpet Cleaning',
    description: 'Deep carpet and upholstery cleaning',
    price: 90,
    duration: '2-3 hours',
    icon: '🧽',
    rating: 4.5,
    reviewCount: 45,
    category: 'cleaning',
    features: ['Steam cleaning', 'Stain removal', 'Sanitization'],
  },
];

const popularSearches = [
  'Plumber',
  'Electrician',
  'House Cleaning',
  'AC Service',
  'Beauty Service',
  'Carpet Cleaning',
  'Kitchen Cleaning',
  'Bathroom Cleaning',
];

const recentSearches = [
  'Plumber near me',
  'AC repair',
  'House cleaning service',
];

export const SearchScreen: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState(allServices);
  const [isSearching, setIsSearching] = useState(false);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setSearchResults(allServices);
      setIsSearching(false);
    } else {
      setIsSearching(true);
      const filtered = allServices.filter(service =>
        service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        service.features.some(feature => 
          feature.toLowerCase().includes(searchQuery.toLowerCase())
        )
      );
      setSearchResults(filtered);
    }
  }, [searchQuery]);

  const handleBack = () => {
    router.back();
  };

  const handleServicePress = (serviceId: string) => {
    router.push(`/customer/service-detail/${serviceId}`);
  };

  const handleBookPress = (serviceId: string) => {
    router.push(`/customer/service-detail/${serviceId}`);
  };

  const handlePopularSearchPress = (search: string) => {
    setSearchQuery(search);
  };

  const handleRecentSearchPress = (search: string) => {
    setSearchQuery(search);
  };

  const clearSearch = () => {
    setSearchQuery('');
  };

  const renderServiceItem = ({ item }: { item: typeof allServices[0] }) => (
    <ServiceCard
      id={item.id}
      name={item.name}
      description={item.description}
      price={item.price}
      duration={item.duration}
      icon={item.icon}
      rating={item.rating}
      reviewCount={item.reviewCount}
      features={item.features}
      onPress={handleServicePress}
      onAddPress={handleBookPress}
      variant="compact"
      style={searchScreenStyles.serviceCard}
    />
  );

  return (
    <SafeAreaView style={searchScreenStyles.container}>
      {/* Top Navigation */}
      <TopNavbar
        title="Search"
        showBack={true}
        showNotification={true}
        showCart={true}
        onBackPress={handleBack}
      />

      {/* Search Input */}
      <View style={searchScreenStyles.searchContainer}>
        <View style={searchScreenStyles.searchInputContainer}>
          <Text style={searchScreenStyles.searchIcon}>🔍</Text>
          <TextInput
            style={searchScreenStyles.searchInput}
            placeholder="Search for services..."
            value={searchQuery}
            onChangeText={setSearchQuery}
            autoFocus
            returnKeyType="search"
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity style={searchScreenStyles.clearButton} onPress={clearSearch}>
              <Text style={searchScreenStyles.clearIcon}>✕</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Content */}
      <ScrollView style={searchScreenStyles.content} showsVerticalScrollIndicator={false}>
        {!isSearching && searchQuery.trim() === '' ? (
          <>
            {/* Recent Searches */}
            {recentSearches.length > 0 && (
              <View style={searchScreenStyles.section}>
                <Text style={searchScreenStyles.sectionTitle}>Recent Searches</Text>
                {recentSearches.map((search, index) => (
                  <TouchableOpacity
                    key={index}
                    style={searchScreenStyles.searchItem}
                    onPress={() => handleRecentSearchPress(search)}
                  >
                    <Text style={searchScreenStyles.searchItemIcon}>🕒</Text>
                    <Text style={searchScreenStyles.searchItemText}>{search}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}

            {/* Popular Searches */}
            <View style={searchScreenStyles.section}>
              <Text style={searchScreenStyles.sectionTitle}>Popular Searches</Text>
              <View style={searchScreenStyles.popularSearchesGrid}>
                {popularSearches.map((search, index) => (
                  <TouchableOpacity
                    key={index}
                    style={searchScreenStyles.popularSearchChip}
                    onPress={() => handlePopularSearchPress(search)}
                  >
                    <Text style={searchScreenStyles.popularSearchText}>{search}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </>
        ) : (
          <>
            {/* Search Results */}
            <View style={searchScreenStyles.section}>
              <Text style={searchScreenStyles.sectionTitle}>
                {searchResults.length > 0 
                  ? `${searchResults.length} results found`
                  : 'No results found'
                }
              </Text>
              
              {searchResults.length > 0 ? (
                <FlatList
                  data={searchResults}
                  renderItem={renderServiceItem}
                  keyExtractor={(item) => item.id}
                  scrollEnabled={false}
                  showsVerticalScrollIndicator={false}
                />
              ) : (
                <View style={searchScreenStyles.noResultsContainer}>
                  <Text style={searchScreenStyles.noResultsText}>
                    No services found for {searchQuery}
                  </Text>
                  <Text style={searchScreenStyles.noResultsSubtext}>
                    Try searching with different keywords
                  </Text>
                </View>
              )}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};
