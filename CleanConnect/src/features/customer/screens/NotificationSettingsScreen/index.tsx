import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  SafeAreaView,
  Switch,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '../../../../components/common/Button';
import { notificationService, NotificationSettings } from '../../../../lib/pushNotifications';
import { notificationSettingsScreenStyles } from '../../styles/NotificationSettingsScreen.styles';

const notificationTypes = [
  {
    id: 'bookingUpdates',
    title: 'Booking Updates',
    description: 'Get notified about booking confirmations, status changes, and progress updates',
    icon: '📋',
  },
  {
    id: 'messages',
    title: 'Messages',
    description: 'Receive notifications when providers send you messages',
    icon: '💬',
  },
  {
    id: 'payments',
    title: 'Payment Notifications',
    description: 'Get notified about payment confirmations and receipts',
    icon: '💳',
  },
  {
    id: 'reminders',
    title: 'Reminders',
    description: 'Receive reminders about upcoming bookings and appointments',
    icon: '⏰',
  },
  {
    id: 'promotions',
    title: 'Promotions & Deals',
    description: 'Get notified about special offers, discounts, and promotions',
    icon: '🎉',
  },
];

export const NotificationSettingsScreen: React.FC = () => {
  const [settings, setSettings] = useState<NotificationSettings>({
    bookingUpdates: true,
    messages: true,
    payments: true,
    reminders: true,
    promotions: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [notificationsEnabled, setNotificationsEnabled] = useState(false);

  useEffect(() => {
    loadNotificationSettings();
    checkNotificationPermissions();
  }, []);

  const loadNotificationSettings = async () => {
    try {
      const userId = 'user-123'; // In real app, get from auth store
      const userSettings = await notificationService.getNotificationSettings(userId);
      if (userSettings) {
        setSettings(userSettings);
      }
    } catch (error) {
      console.error('Error loading notification settings:', error);
    }
  };

  const checkNotificationPermissions = async () => {
    try {
      const enabled = await notificationService.areNotificationsEnabled();
      setNotificationsEnabled(enabled);
    } catch (error) {
      console.error('Error checking notification permissions:', error);
    }
  };

  const handleToggleSetting = (key: keyof NotificationSettings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const handleSaveSettings = async () => {
    setIsLoading(true);
    try {
      const userId = 'user-123'; // In real app, get from auth store
      const success = await notificationService.updateNotificationSettings(userId, settings);
      
      if (success) {
        Alert.alert(
          'Settings Saved',
          'Your notification preferences have been updated successfully.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Error',
          'Failed to save notification settings. Please try again.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error saving notification settings:', error);
      Alert.alert(
        'Error',
        'An unexpected error occurred. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleRequestPermissions = async () => {
    try {
      const granted = await notificationService.requestPermissions();
      if (granted) {
        setNotificationsEnabled(true);
        Alert.alert(
          'Permissions Granted',
          'You will now receive push notifications for important updates.',
          [{ text: 'OK' }]
        );
      } else {
        Alert.alert(
          'Permissions Denied',
          'To receive notifications, please enable them in your device settings.',
          [{ text: 'OK' }]
        );
      }
    } catch (error) {
      console.error('Error requesting notification permissions:', error);
    }
  };

  const handleTestNotification = async () => {
    try {
      await mockPushNotificationService.scheduleLocalNotification(
        'Test Notification',
        'This is a test notification to verify your settings are working correctly.',
        {
          type: 'reminder',
          title: 'Test Notification',
          body: 'This is a test notification to verify your settings are working correctly.',
        }
      );
      
      Alert.alert(
        'Test Notification Sent',
        'You should receive a test notification shortly.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('Error sending test notification:', error);
      Alert.alert(
        'Error',
        'Failed to send test notification. Please check your settings.',
        [{ text: 'OK' }]
      );
    }
  };

  const renderNotificationType = (type: typeof notificationTypes[0]) => {
    const settingKey = type.id as keyof NotificationSettings;
    const isEnabled = settings[settingKey];

    return (
      <View key={type.id} style={notificationSettingsScreenStyles.notificationTypeCard}>
        <View style={notificationSettingsScreenStyles.notificationTypeHeader}>
          <View style={notificationSettingsScreenStyles.notificationTypeInfo}>
            <Text style={notificationSettingsScreenStyles.notificationTypeIcon}>{type.icon}</Text>
            <View style={notificationSettingsScreenStyles.notificationTypeText}>
              <Text style={notificationSettingsScreenStyles.notificationTypeTitle}>{type.title}</Text>
              <Text style={notificationSettingsScreenStyles.notificationTypeDescription}>{type.description}</Text>
            </View>
          </View>
          <Switch
            value={isEnabled}
            onValueChange={() => handleToggleSetting(settingKey)}
            trackColor={{ false: colors.gray[300], true: colors.primary }}
            thumbColor={colors.white}
            disabled={!notificationsEnabled}
          />
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={notificationSettingsScreenStyles.container}>
      {/* Header */}
      <View style={notificationSettingsScreenStyles.header}>
        <Text style={notificationSettingsScreenStyles.headerTitle}>Notification Settings</Text>
        <Text style={notificationSettingsScreenStyles.headerSubtitle}>Manage your notification preferences</Text>
      </View>

      <ScrollView style={notificationSettingsScreenStyles.content} showsVerticalScrollIndicator={false}>
        {/* Notification Status */}
        <View style={notificationSettingsScreenStyles.statusCard}>
          <View style={notificationSettingsScreenStyles.statusHeader}>
            <Text style={notificationSettingsScreenStyles.statusIcon}>
              {notificationsEnabled ? '🔔' : '🔕'}
            </Text>
            <View style={notificationSettingsScreenStyles.statusInfo}>
              <Text style={notificationSettingsScreenStyles.statusTitle}>
                {notificationsEnabled ? 'Notifications Enabled' : 'Notifications Disabled'}
              </Text>
              <Text style={notificationSettingsScreenStyles.statusDescription}>
                {notificationsEnabled 
                  ? 'You will receive notifications based on your preferences below.'
                  : 'Enable notifications to stay updated about your bookings and messages.'
                }
              </Text>
            </View>
          </View>
          {!notificationsEnabled && (
            <Button
              title="Enable Notifications"
              onPress={handleRequestPermissions}
              style={notificationSettingsScreenStyles.enableButton}
            />
          )}
        </View>

        {/* Notification Types */}
        <View style={notificationSettingsScreenStyles.section}>
          <Text style={notificationSettingsScreenStyles.sectionTitle}>Notification Types</Text>
          <Text style={notificationSettingsScreenStyles.sectionDescription}>
            Choose which types of notifications you want to receive
          </Text>
          
          {notificationTypes.map(renderNotificationType)}
        </View>

        {/* Test Notification */}
        {notificationsEnabled && (
          <View style={notificationSettingsScreenStyles.section}>
            <Text style={notificationSettingsScreenStyles.sectionTitle}>Test Notifications</Text>
            <Text style={notificationSettingsScreenStyles.sectionDescription}>
              Send a test notification to verify your settings are working correctly
            </Text>
            <Button
              title="Send Test Notification"
              onPress={handleTestNotification}
              style={notificationSettingsScreenStyles.testButton}
            />
          </View>
        )}

        {/* Save Button */}
        <View style={notificationSettingsScreenStyles.saveSection}>
          <Button
            title={isLoading ? 'Saving...' : 'Save Settings'}
            onPress={handleSaveSettings}
            disabled={isLoading}
            style={notificationSettingsScreenStyles.saveButton}
          />
        </View>

        {/* Bottom Spacing */}
        <View style={notificationSettingsScreenStyles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
};

