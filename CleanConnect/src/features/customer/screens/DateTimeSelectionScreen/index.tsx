import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  TextInput,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useBookingStore } from '../../../../store/bookingStore';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { styles } from './styles';

const mockProviders = [
  {
    id: '1',
    name: '<PERSON><PERSON>',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    location: 'Barcelona, Spain',
    rating: 4.94,
    totalReviews: 584,
    totalHired: 767,
    experience: '5+ Years',
    description: 'Skilled spa specialist with over 5 years of experience in providing...',
    isPremium: true,
    isBasic: false,
    reviews: [],
  },
  {
    id: '2',
    name: '<PERSON>',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    location: 'Barcelona, Spain',
    rating: 4.94,
    totalReviews: 432,
    totalHired: 523,
    experience: '3+ Years',
    description: 'Professional cleaner with expertise in home cleaning services...',
    isPremium: false,
    isBasic: true,
    reviews: [],
  },
];

export function DateTimeSelectionScreen() {
  const {
    isAutoAssign,
    selectedProvider,
    jobInfo,
    frequency,
    total,
    setAutoAssign,
    setSelectedProvider,
    setJobInfo,
    setFrequency,
  } = useBookingStore();

  const [selectedMonth, setSelectedMonth] = useState('November 2024');

  const handleProviderSelect = (provider: typeof mockProviders[0]) => {
    setSelectedProvider(provider);
    setAutoAssign(false);
  };

  const handleAutoAssign = () => {
    setAutoAssign(true);
    setSelectedProvider(null);
  };

  const handleSetTime = () => {
    router.push('/customer/booking/confirm-review');
  };

  const handleProviderProfile = (provider: typeof mockProviders[0]) => {
    router.push(`/customer/booking/provider-profile?id=${provider.id}`);
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Time & Date</Text>
        <Text style={styles.stepIndicator}>Step 2 of 3</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Auto-Assign Option */}
        <TouchableOpacity
          style={[styles.autoAssignCard, isAutoAssign && styles.autoAssignSelected]}
          onPress={handleAutoAssign}
        >
          <View style={styles.autoAssignContent}>
            <View style={styles.autoAssignLeft}>
              <Text style={styles.autoAssignBadge}>🏠 Same day</Text>
              <Text style={styles.autoAssignTitle}>Auto-Assign</Text>
              <Text style={styles.autoAssignSubtitle}>Assign the best professional</Text>
            </View>
            {isAutoAssign && (
              <Ionicons name="checkmark-circle" size={24} color="#FF6B35" />
            )}
          </View>
        </TouchableOpacity>

        {/* Provider Selection */}
        <Text style={styles.sectionTitle}>Which professional?</Text>
        <View style={styles.providersContainer}>
          {mockProviders.map((provider) => (
            <TouchableOpacity
              key={provider.id}
              style={[
                styles.providerCard,
                selectedProvider?.id === provider.id && styles.providerSelected,
              ]}
              onPress={() => handleProviderSelect(provider)}
            >
              <Image source={{ uri: provider.avatar }} style={styles.providerAvatar} />
              <View style={styles.providerBadge}>
                <Text style={styles.providerBadgeText}>
                  {provider.isPremium ? '💎 Premium' : '🔶 Basic'}
                </Text>
              </View>
              <TouchableOpacity
                style={styles.providerInfo}
                onPress={() => handleProviderProfile(provider)}
              >
                <Text style={styles.providerName}>{provider.name}</Text>
                <Text style={styles.providerRecommended}>Recommended</Text>
                <View style={styles.providerRating}>
                  <Ionicons name="star" size={12} color="#FFB800" />
                  <Text style={styles.ratingText}>{provider.rating}</Text>
                </View>
              </TouchableOpacity>
            </TouchableOpacity>
          ))}
        </View>

        {/* Job Info */}
        <Text style={styles.sectionTitle}>About the job info</Text>
        <TextInput
          style={styles.jobInfoInput}
          placeholder="Tell us more about your cleaning needs..."
          multiline
          numberOfLines={4}
          value={jobInfo}
          onChangeText={setJobInfo}
          textAlignVertical="top"
        />

        {/* Frequency Selection */}
        <View style={styles.frequencyContainer}>
          {['One time', 'Weekly', 'Monthly'].map((freq) => (
            <TouchableOpacity
              key={freq}
              style={[
                styles.frequencyButton,
                frequency === freq.toLowerCase().replace(' ', '-') && styles.frequencySelected,
              ]}
              onPress={() => setFrequency(freq.toLowerCase().replace(' ', '-') as any)}
            >
              <Text
                style={[
                  styles.frequencyText,
                  frequency === freq.toLowerCase().replace(' ', '-') && styles.frequencyTextSelected,
                ]}
              >
                {freq}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Date Picker */}
        <Text style={styles.sectionTitle}>Pick start service date?</Text>
        <View style={styles.datePickerHeader}>
          <TouchableOpacity>
            <Ionicons name="chevron-back" size={20} color="#666" />
          </TouchableOpacity>
          <Text style={styles.monthText}>{selectedMonth}</Text>
          <TouchableOpacity>
            <Ionicons name="chevron-forward" size={20} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Calendar placeholder - would implement full calendar here */}
        <View style={styles.calendarPlaceholder}>
          <Text style={styles.calendarText}>Calendar component would go here</Text>
        </View>

        <View style={styles.spacer} />
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Start from</Text>
          <Text style={styles.priceAmount}>${total.toFixed(2)}</Text>
        </View>
        <TouchableOpacity style={styles.setTimeButton} onPress={handleSetTime}>
          <Text style={styles.setTimeButtonText}>Set Time</Text>
        </TouchableOpacity>
      </View>

      <CustomerNavigation />
    </View>
  );
}
