import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  } as ViewStyle,

  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  stepIndicator: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
  } as ViewStyle,

  autoAssignCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.xl,
    borderWidth: 2,
    borderColor: colors.gray[200],
  } as ViewStyle,

  autoAssignSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  } as ViewStyle,

  autoAssignContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  autoAssignLeft: {
    flex: 1,
  } as ViewStyle,

  autoAssignBadge: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: 4,
    borderRadius: 12,
    alignSelf: 'flex-start',
    marginBottom: spacing.xs,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  autoAssignTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: 2,
  } as TextStyle,

  autoAssignSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
    marginTop: spacing.lg,
  } as TextStyle,

  providersContainer: {
    flexDirection: 'row',
    gap: spacing.md,
    marginBottom: spacing.xl,
  } as ViewStyle,

  providerCard: {
    flex: 1,
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: colors.gray[200],
    position: 'relative',
  } as ViewStyle,

  providerSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '10',
  } as ViewStyle,

  providerAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    marginBottom: spacing.sm,
  } as ViewStyle,

  providerBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  } as ViewStyle,

  providerBadgeText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  providerInfo: {
    alignItems: 'center',
  } as ViewStyle,

  providerName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    marginBottom: 2,
  } as TextStyle,

  providerRecommended: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    fontFamily: typography.fontFamily.regular,
    marginBottom: spacing.xs,
  } as TextStyle,

  providerRating: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  } as ViewStyle,

  ratingText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  jobInfoInput: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    borderWidth: 1,
    borderColor: colors.gray[200],
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.gray[900],
    minHeight: 100,
    marginBottom: spacing.xl,
  } as ViewStyle,

  frequencyContainer: {
    flexDirection: 'row',
    gap: spacing.sm,
    marginBottom: spacing.xl,
  } as ViewStyle,

  frequencyButton: {
    flex: 1,
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.lg,
    borderRadius: 12,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
  } as ViewStyle,

  frequencySelected: {
    backgroundColor: colors.gray[900],
  } as ViewStyle,

  frequencyText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  frequencyTextSelected: {
    color: colors.white,
  } as TextStyle,

  datePickerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  } as ViewStyle,

  monthText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  calendarPlaceholder: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 200,
    marginBottom: spacing.xl,
  } as ViewStyle,

  calendarText: {
    fontSize: typography.sizes.md,
    color: colors.gray[500],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  spacer: {
    height: 100,
  } as ViewStyle,

  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  priceContainer: {
    flex: 1,
  } as ViewStyle,

  priceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  } as TextStyle,

  priceAmount: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  setTimeButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 16,
    minWidth: 120,
    alignItems: 'center',
  } as ViewStyle,

  setTimeButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
});
