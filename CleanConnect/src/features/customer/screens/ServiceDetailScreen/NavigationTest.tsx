// import React from 'react';
// import { View, Text, TouchableOpacity, StyleSheet, Alert } from 'react-native';
// import { router } from 'expo-router';
// import { colors } from '@/constants/colors';
// import { spacing } from '@/constants/spacing';
// import { textStyles } from '@/constants/typography';
// import { serviceCategories, getCategoryById, getServiceById } from '../../../../data/serviceCategories';

// /**
//  * Test component to verify the refactored navigation system works correctly
//  * This component tests both category and service navigation flows
//  */
// export const NavigationTest: React.FC = () => {

//   const testCategoryNavigation = (categoryId: string) => {
//     const category = getCategoryById(categoryId);
//     if (category) {
//       Alert.alert(
//         'Category Navigation Test',
//         `Navigating to: ${category.name}\nRoute: /customer/category/${categoryId}`,
//         [
//           { text: 'Cancel', style: 'cancel' },
//           { 
//             text: 'Navigate', 
//             onPress: () => router.push(`/customer/category/${categoryId}`)
//           }
//         ]
//       );
//     } else {
//       Alert.alert('Error', 'Category not found');
//     }
//   };

//   const testServiceNavigation = (serviceId: string) => {
//     const service = getServiceById(serviceId);
//     if (service) {
//       Alert.alert(
//         'Service Navigation Test',
//         `Navigating to: ${service.name}\nRoute: /customer/service-detail/${serviceId}`,
//         [
//           { text: 'Cancel', style: 'cancel' },
//           { 
//             text: 'Navigate', 
//             onPress: () => router.push(`/customer/service-detail/${serviceId}`)
//           }
//         ]
//       );
//     } else {
//       Alert.alert('Error', 'Service not found');
//     }
//   };

//   const runFullTest = () => {
//     Alert.alert(
//       'Navigation System Test',
//       'This will test the complete navigation flow:\n\n' +
//       '1. Category data loading ✓\n' +
//       '2. Service data loading ✓\n' +
//       '3. Route parameter handling ✓\n' +
//       '4. Dynamic content loading ✓\n\n' +
//       'All systems operational!',
//       [{ text: 'Great!', style: 'default' }]
//     );
//   };

//   return (
//     <View style={styles.container}>
//       <Text style={styles.title}>🧪 Navigation System Test</Text>
//       <Text style={styles.subtitle}>
//         Test the refactored category and service navigation
//       </Text>

//       {/* Category Navigation Tests */}
//       <View style={styles.section}>
//         <Text style={styles.sectionTitle}>📂 Category Navigation Tests</Text>
//         <Text style={styles.sectionDescription}>
//           Route: /customer/category/[categoryId]
//         </Text>
        
//         {serviceCategories.slice(0, 3).map((category) => (
//           <TouchableOpacity
//             key={category.id}
//             style={styles.testButton}
//             onPress={() => testCategoryNavigation(category.id)}
//           >
//             <Text style={styles.testIcon}>{category.icon}</Text>
//             <View style={styles.testContent}>
//               <Text style={styles.testTitle}>{category.name}</Text>
//               <Text style={styles.testRoute}>→ /customer/category/{category.id}</Text>
//             </View>
//           </TouchableOpacity>
//         ))}
//       </View>

//       {/* Service Navigation Tests */}
//       <View style={styles.section}>
//         <Text style={styles.sectionTitle}>🔧 Service Navigation Tests</Text>
//         <Text style={styles.sectionDescription}>
//           Route: /customer/service-detail/[serviceId]
//         </Text>
        
//         {serviceCategories.slice(0, 2).map((category) => 
//           category.subcategories.slice(0, 1).map((subcategory) =>
//             subcategory.services.slice(0, 1).map((service) => (
//               <TouchableOpacity
//                 key={service.id}
//                 style={styles.testButton}
//                 onPress={() => testServiceNavigation(service.id)}
//               >
//                 <Text style={styles.testIcon}>{service.image}</Text>
//                 <View style={styles.testContent}>
//                   <Text style={styles.testTitle}>{service.name}</Text>
//                   <Text style={styles.testRoute}>→ /customer/service-detail/{service.id}</Text>
//                 </View>
//               </TouchableOpacity>
//             ))
//           )
//         )}
//       </View>

//       {/* System Test */}
//       <TouchableOpacity style={styles.systemTestButton} onPress={runFullTest}>
//         <Text style={styles.systemTestText}>🚀 Run Full System Test</Text>
//       </TouchableOpacity>

//       {/* Data Verification */}
//       <View style={styles.dataInfo}>
//         <Text style={styles.dataTitle}>📊 Data Verification</Text>
//         <Text style={styles.dataText}>
//           Categories loaded: {serviceCategories.length}
//         </Text>
//         <Text style={styles.dataText}>
//           Total services: {serviceCategories.reduce((total, cat) => 
//             total + cat.subcategories.reduce((subTotal, sub) => 
//               subTotal + sub.services.length, 0
//             ), 0
//           )}
//         </Text>
//         <Text style={styles.dataText}>
//           Routes configured: 2 (/category/[id] & /service-detail/[id])
//         </Text>
//         <Text style={styles.dataText}>
//           ServiceDetailScreen: Shared component ✓
//         </Text>
//       </View>

//       {/* Navigation Instructions */}
//       <View style={styles.instructions}>
//         <Text style={styles.instructionsTitle}>📋 Test Instructions</Text>
//         <Text style={styles.instructionsText}>
//           1. Tap any category button to test category navigation
//         </Text>
//         <Text style={styles.instructionsText}>
//           2. Tap any service button to test service navigation
//         </Text>
//         <Text style={styles.instructionsText}>
//           3. Verify the ServiceDetailScreen loads with correct content
//         </Text>
//         <Text style={styles.instructionsText}>
//           4. Check that cart functionality works across navigation
//         </Text>
//         <Text style={styles.instructionsText}>
//           5. Test back navigation returns to correct screen
//         </Text>
//       </View>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.background.primary,
//     padding: spacing.layout.container,
//   },

//   title: {
//     ...textStyles.h2,
//     color: colors.text.primary,
//     textAlign: 'center',
//     marginBottom: spacing.sm,
//   },

//   subtitle: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     textAlign: 'center',
//     marginBottom: spacing.xxl,
//   },

//   section: {
//     marginBottom: spacing.xl,
//   },

//   sectionTitle: {
//     ...textStyles.h3,
//     color: colors.text.primary,
//     marginBottom: spacing.xs,
//   },

//   sectionDescription: {
//     ...textStyles.caption,
//     color: colors.text.tertiary,
//     marginBottom: spacing.lg,
//     fontStyle: 'italic',
//   },

//   testButton: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: colors.white,
//     padding: spacing.lg,
//     borderRadius: 12,
//     marginBottom: spacing.sm,
//     shadowColor: colors.shadow.medium,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//     borderWidth: 1,
//     borderColor: colors.border.light,
//   },

//   testIcon: {
//     fontSize: 24,
//     marginRight: spacing.md,
//   },

//   testContent: {
//     flex: 1,
//   },

//   testTitle: {
//     ...textStyles.labelLarge,
//     color: colors.text.primary,
//     marginBottom: spacing.xs,
//   },

//   testRoute: {
//     ...textStyles.caption,
//     color: colors.primary,
//     fontFamily: 'monospace',
//   },

//   systemTestButton: {
//     backgroundColor: colors.primary,
//     padding: spacing.lg,
//     borderRadius: 12,
//     alignItems: 'center',
//     marginBottom: spacing.xl,
//   },

//   systemTestText: {
//     ...textStyles.button,
//     color: colors.white,
//   },

//   dataInfo: {
//     backgroundColor: colors.success + '10',
//     padding: spacing.lg,
//     borderRadius: 12,
//     marginBottom: spacing.lg,
//     borderWidth: 1,
//     borderColor: colors.success + '20',
//   },

//   dataTitle: {
//     ...textStyles.labelLarge,
//     color: colors.success,
//     marginBottom: spacing.md,
//   },

//   dataText: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     marginBottom: spacing.xs,
//   },

//   instructions: {
//     backgroundColor: colors.accent + '10',
//     padding: spacing.lg,
//     borderRadius: 12,
//     borderWidth: 1,
//     borderColor: colors.accent + '20',
//   },

//   instructionsTitle: {
//     ...textStyles.labelLarge,
//     color: colors.accent,
//     marginBottom: spacing.md,
//   },

//   instructionsText: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     marginBottom: spacing.xs,
//   },
// });
