# Service Detail Screen - Refactored for Dynamic Categories

A comprehensive, dynamic service detail screen that serves as a shared component for all service categories. Users can navigate from category cards on the home screen or specific service links to this unified screen, which dynamically loads and displays appropriate content based on the selected category or service.

## 🔄 **Refactoring Overview**

The CleanConnect app has been refactored to use a **shared ServiceDetailScreen** that dynamically loads content based on navigation parameters:

- **Category Navigation**: `/customer/category/[categoryId]` - Shows all services in a category
- **Service Navigation**: `/customer/service-detail/[serviceId]` - Shows specific service details
- **Dynamic Content**: Same component, different data based on route parameters
- **Centralized Data**: All service data managed in `src/data/serviceCategories.ts`

## 🎯 **Navigation Flow**

### From Home Screen (Category Navigation)
```
Home Screen → Category Card → /customer/category/[categoryId] → ServiceDetailScreen
```

### From Search/Lists (Service Navigation)
```
Search Results → Service Card → /customer/service-detail/[serviceId] → ServiceDetailScreen
```

### Dynamic Content Loading
The same `ServiceDetailScreen` component intelligently determines what to display:
- **Category Mode**: Shows all subcategories and services for a category
- **Service Mode**: Shows the category containing the specific service

## ✅ **Enhanced Features**

### **Dynamic Content System**
- **Smart Navigation**: Detects categoryId vs serviceId parameters
- **Unified Interface**: Same screen for all service types
- **Centralized Data**: Single source of truth for all service information
- **Easy Expansion**: Add new categories without code changes

### **Core Functionality**
- **Service Overview**: Display service name, rating, and total bookings
- **Subcategory Navigation**: Browse different service subcategories with visual icons
- **Service Cards**: Utilize the improved ServiceCard component for consistent design
- **Cart Management**: Add/remove items with quantity controls
- **Responsive Design**: Adapts to different screen sizes
- **Seamless Navigation**: Integrated with app-wide navigation patterns

### ✅ **Design Elements**
- **Header**: Back button, service title, search and share actions
- **Promotion Banner**: Highlight special offers and discounts
- **Category Images**: Visual representation of service categories
- **Service Listings**: Clean card-based layout for services
- **Floating Menu**: Quick access menu button (as shown in design)
- **Cart Bar**: Persistent bottom bar showing cart total and view cart action

### ✅ **Interactive Features**
- **Quantity Controls**: +/- buttons for adjusting service quantities
- **Category Selection**: Switch between different service categories
- **Service Navigation**: Navigate to booking flow or service customization
- **Cart Management**: Real-time cart total calculation
- **Share Functionality**: Share service with others

## Usage

## 🚀 **Navigation Examples**

### Category Navigation (from Home Screen)
```typescript
// Navigate to category-based service detail
const handleCategoryPress = (categoryId: string) => {
  router.push(`/customer/category/${categoryId}`);
};

// Examples:
router.push('/customer/category/house-cleaning');
router.push('/customer/category/air-conditioner');
router.push('/customer/category/deep-cleaning');
```

### Service Navigation (from Search/Lists)
```typescript
// Navigate to specific service detail
const handleServicePress = (serviceId: string) => {
  router.push(`/customer/service-detail/${serviceId}`);
};

// Examples:
router.push('/customer/service-detail/premium-deep-cleaning');
router.push('/customer/service-detail/jet-spray-service');
```

### Route Configuration

Two routes point to the same component:
- `/customer/category/[categoryId]` - Category-based navigation
- `/customer/service-detail/[serviceId]` - Service-based navigation

Both routes use: `ServiceDetailScreen` component

## 📊 **Data Structure**

### Centralized Service Data (`src/data/serviceCategories.ts`)

```typescript
interface ServiceCategory {
  id: string;                    // 'house-cleaning', 'air-conditioner'
  name: string;                  // 'House Cleaning', 'Air Conditioner'
  icon: string;                  // '🏠', '❄️'
  color: string;                 // Brand colors
  description: string;           // Category description
  rating: number;                // Average category rating
  totalBookings: string;         // '2.1M', '850K'
  images: CategoryImage[];       // Visual subcategory representations
  subcategories: ServiceSubcategory[];  // Service groupings
  promotions: Promotion[];       // Category-specific offers
}

interface ServiceSubcategory {
  id: string;                    // 'occupied-apartment', 'ac-service'
  title: string;                 // 'Occupied apartment', 'AC Service & Repair'
  image: string;                 // Visual representation
  services: ServiceItem[];       // Individual services
}

interface ServiceItem {
  id: string;                    // 'premium-deep-cleaning'
  name: string;                  // 'Premium home deep cleaning'
  description: string;           // Service description
  rating: number;                // 4.56
  reviewCount: string;           // '6K'
  startingPrice: number;         // 4499
  originalPrice?: number;        // 5299 (for discounts)
  duration?: string;             // '4-6 hours'
  options: number;               // Number of customization options
  image: string;                 // Service icon/image
  popular?: boolean;             // Featured service flag
  available?: boolean;           // Availability status
  features?: string[];           // Service features list
}
```

## Design System Integration

### Colors
- Uses `colors.primary` for main actions and highlights
- Uses `colors.background.primary` for screen background
- Uses `colors.text.primary/secondary/tertiary` for text hierarchy
- Uses `colors.border.light` for subtle separators

### Typography
- Uses `textStyles.h2` for main service title
- Uses `textStyles.h3` for section titles
- Uses `textStyles.body` for descriptions
- Uses `textStyles.button` for action buttons

### Spacing
- Uses `spacing.layout.container` for screen padding
- Uses `spacing.lg/md/sm` for component spacing
- Uses consistent spacing throughout the interface

## Key Components Used

### ServiceCard
The screen heavily utilizes the improved ServiceCard component:

```typescript
<ServiceCard
  id={service.id}
  name={service.name}
  description={service.description}
  price={service.startingPrice}
  image={service.image}
  note={`${service.options} options`}
  variant="modern"
  onPress={handleServicePress}
  onAddPress={handleServiceAdd}
/>
```

### Quantity Controls
Custom quantity controls that appear when items are added to cart:

```typescript
<View style={serviceDetailStyles.quantityControls}>
  <TouchableOpacity onPress={() => updateQuantity(serviceId, -1)}>
    <Text>−</Text>
  </TouchableOpacity>
  <Text>{cartItems[serviceId]}</Text>
  <TouchableOpacity onPress={() => updateQuantity(serviceId, 1)}>
    <Text>+</Text>
  </TouchableOpacity>
</View>
```

## State Management

### Cart State
- `cartItems`: Object tracking quantity of each service in cart
- `cartTotal`: Calculated total price of all items in cart
- `selectedCategory`: Currently selected service category

### Booking Store Integration
- Integrates with `useBookingStore` for service selection
- Sets selected service when navigating to booking flow
- Maintains booking state across navigation

## Responsive Design

### Screen Adaptation
- Automatically adjusts to different screen sizes
- Category images scale appropriately
- Service cards maintain consistent spacing
- Cart bar adapts to content

### Touch Targets
- All interactive elements have proper touch targets (minimum 44px)
- Quantity controls are easily accessible
- Navigation elements are thumb-friendly

## Performance Considerations

### Optimizations
- Efficient re-rendering with proper state management
- Optimized scroll performance with proper key props
- Lazy loading of service data (ready for API integration)
- Minimal re-calculations of cart totals

## Future Enhancements

### Potential Additions
- **Image Gallery**: Full-screen image viewing
- **Service Filters**: Filter services by price, rating, etc.
- **Favorites**: Save services for later
- **Reviews Section**: Display user reviews and ratings
- **Service Comparison**: Compare multiple services
- **Booking Calendar**: Quick date selection
- **Provider Information**: Show service provider details

## Integration Points

### API Integration
Ready for integration with backend APIs:
- Service data fetching based on serviceId
- Cart management with backend sync
- User preferences and favorites
- Real-time pricing and availability

### Analytics
Ready for analytics integration:
- Service view tracking
- Cart interaction events
- Category selection analytics
- Conversion funnel tracking

This Service Detail screen provides a solid foundation for a modern service marketplace experience while maintaining consistency with the app's design system and user experience patterns.
