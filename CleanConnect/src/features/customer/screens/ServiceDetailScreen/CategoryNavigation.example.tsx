// import React from 'react';
// import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
// import { router } from 'expo-router';
// import { colors } from '@/constants/colors';
// import { spacing } from '@/constants/spacing';
// import { textStyles } from '@/constants/typography';
// import { serviceCategories } from '../../../../data/serviceCategories';

// /**
//  * Example component demonstrating the refactored category navigation system
//  * This shows how users can navigate from categories to the shared ServiceDetailScreen
//  */
// export const CategoryNavigationExample: React.FC = () => {
  
//   const handleCategoryPress = (categoryId: string, categoryName: string) => {
//     console.log(`Navigating to category: ${categoryName} (${categoryId})`);
//     router.push(`/customer/category/${categoryId}`);
//   };

//   const handleServicePress = (serviceId: string, serviceName: string) => {
//     console.log(`Navigating to specific service: ${serviceName} (${serviceId})`);
//     router.push(`/customer/service-detail/${serviceId}`);
//   };

//   return (
//     <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
//       <Text style={styles.title}>Dynamic Category Navigation</Text>
//       <Text style={styles.subtitle}>
//         Tap any category to see the ServiceDetailScreen with dynamic content
//       </Text>

//       {/* Category Navigation */}
//       <View style={styles.section}>
//         <Text style={styles.sectionTitle}>Service Categories</Text>
//         <Text style={styles.sectionDescription}>
//           These navigate to: /customer/category/[categoryId]
//         </Text>
        
//         <View style={styles.categoriesGrid}>
//           {serviceCategories.map((category) => (
//             <TouchableOpacity
//               key={category.id}
//               style={[styles.categoryCard, { borderColor: category.color }]}
//               onPress={() => handleCategoryPress(category.id, category.name)}
//               activeOpacity={0.8}
//             >
//               <View style={[styles.categoryIconContainer, { backgroundColor: category.color + '20' }]}>
//                 <Text style={styles.categoryIcon}>{category.icon}</Text>
//               </View>
//               <Text style={styles.categoryName}>{category.name}</Text>
//               <Text style={styles.categoryStats}>
//                 ⭐ {category.rating} • {category.totalBookings} bookings
//               </Text>
//               <Text style={styles.categorySubcategories}>
//                 {category.subcategories.length} subcategories
//               </Text>
//             </TouchableOpacity>
//           ))}
//         </View>
//       </View>

//       {/* Individual Service Navigation */}
//       <View style={styles.section}>
//         <Text style={styles.sectionTitle}>Individual Services</Text>
//         <Text style={styles.sectionDescription}>
//           These navigate to: /customer/service-detail/[serviceId]
//         </Text>
        
//         {serviceCategories.map((category) => (
//           <View key={category.id} style={styles.serviceGroup}>
//             <Text style={styles.serviceGroupTitle}>{category.name} Services</Text>
//             {category.subcategories.flatMap(subcat => subcat.services).slice(0, 2).map((service) => (
//               <TouchableOpacity
//                 key={service.id}
//                 style={styles.serviceCard}
//                 onPress={() => handleServicePress(service.id, service.name)}
//                 activeOpacity={0.8}
//               >
//                 <View style={styles.serviceContent}>
//                   <Text style={styles.serviceIcon}>{service.image}</Text>
//                   <View style={styles.serviceInfo}>
//                     <Text style={styles.serviceName}>{service.name}</Text>
//                     <Text style={styles.serviceDescription} numberOfLines={2}>
//                       {service.description}
//                     </Text>
//                     <Text style={styles.servicePrice}>₹{service.startingPrice}</Text>
//                   </View>
//                 </View>
//                 <Text style={styles.arrow}>→</Text>
//               </TouchableOpacity>
//             ))}
//           </View>
//         ))}
//       </View>

//       {/* Navigation Flow Info */}
//       <View style={styles.infoBox}>
//         <Text style={styles.infoTitle}>🔄 Navigation Flow</Text>
//         <Text style={styles.infoText}>
//           <Text style={styles.bold}>Category Navigation:</Text>{'\n'}
//           Home Screen → Category Card → /customer/category/[categoryId] → ServiceDetailScreen
//         </Text>
//         <Text style={styles.infoText}>
//           <Text style={styles.bold}>Service Navigation:</Text>{'\n'}
//           Search/List → Service Card → /customer/service-detail/[serviceId] → ServiceDetailScreen
//         </Text>
//         <Text style={styles.infoText}>
//           <Text style={styles.bold}>Dynamic Content:</Text>{'\n'}
//           The same ServiceDetailScreen component loads different content based on the route parameters
//         </Text>
//       </View>

//       {/* Features Info */}
//       <View style={styles.infoBox}>
//         <Text style={styles.infoTitle}>✨ Key Features</Text>
//         <Text style={styles.infoText}>• Single ServiceDetailScreen for all categories</Text>
//         <Text style={styles.infoText}>• Dynamic content loading based on categoryId/serviceId</Text>
//         <Text style={styles.infoText}>• Consistent UI across all service types</Text>
//         <Text style={styles.infoText}>• Centralized service data management</Text>
//         <Text style={styles.infoText}>• Easy to add new categories and services</Text>
//         <Text style={styles.infoText}>• Maintains cart state across navigation</Text>
//       </View>
//     </ScrollView>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.background.primary,
//     padding: spacing.layout.container,
//   },

//   title: {
//     ...textStyles.h2,
//     color: colors.text.primary,
//     textAlign: 'center',
//     marginBottom: spacing.sm,
//   },

//   subtitle: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     textAlign: 'center',
//     marginBottom: spacing.xxl,
//   },

//   section: {
//     marginBottom: spacing.xxl,
//   },

//   sectionTitle: {
//     ...textStyles.h3,
//     color: colors.text.primary,
//     marginBottom: spacing.xs,
//   },

//   sectionDescription: {
//     ...textStyles.caption,
//     color: colors.text.tertiary,
//     marginBottom: spacing.lg,
//     fontStyle: 'italic',
//   },

//   categoriesGrid: {
//     flexDirection: 'row',
//     flexWrap: 'wrap',
//     gap: spacing.md,
//   },

//   categoryCard: {
//     width: '48%',
//     backgroundColor: colors.white,
//     padding: spacing.lg,
//     borderRadius: 12,
//     borderWidth: 2,
//     alignItems: 'center',
//     shadowColor: colors.shadow.medium,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },

//   categoryIconContainer: {
//     width: 50,
//     height: 50,
//     borderRadius: 25,
//     alignItems: 'center',
//     justifyContent: 'center',
//     marginBottom: spacing.sm,
//   },

//   categoryIcon: {
//     fontSize: 24,
//   },

//   categoryName: {
//     ...textStyles.labelLarge,
//     color: colors.text.primary,
//     textAlign: 'center',
//     marginBottom: spacing.xs,
//   },

//   categoryStats: {
//     ...textStyles.caption,
//     color: colors.text.secondary,
//     textAlign: 'center',
//     marginBottom: spacing.xs,
//   },

//   categorySubcategories: {
//     ...textStyles.caption,
//     color: colors.primary,
//     textAlign: 'center',
//   },

//   serviceGroup: {
//     marginBottom: spacing.lg,
//   },

//   serviceGroupTitle: {
//     ...textStyles.labelLarge,
//     color: colors.text.primary,
//     marginBottom: spacing.md,
//   },

//   serviceCard: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     backgroundColor: colors.white,
//     padding: spacing.lg,
//     borderRadius: 12,
//     marginBottom: spacing.sm,
//     shadowColor: colors.shadow.medium,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//   },

//   serviceContent: {
//     flex: 1,
//     flexDirection: 'row',
//     alignItems: 'center',
//   },

//   serviceIcon: {
//     fontSize: 32,
//     marginRight: spacing.md,
//   },

//   serviceInfo: {
//     flex: 1,
//   },

//   serviceName: {
//     ...textStyles.labelLarge,
//     color: colors.text.primary,
//     marginBottom: spacing.xs,
//   },

//   serviceDescription: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     marginBottom: spacing.xs,
//   },

//   servicePrice: {
//     ...textStyles.button,
//     color: colors.primary,
//   },

//   arrow: {
//     ...textStyles.h3,
//     color: colors.primary,
//   },

//   infoBox: {
//     backgroundColor: colors.primary + '10',
//     padding: spacing.lg,
//     borderRadius: 12,
//     marginBottom: spacing.lg,
//     borderWidth: 1,
//     borderColor: colors.primary + '20',
//   },

//   infoTitle: {
//     ...textStyles.labelLarge,
//     color: colors.primary,
//     marginBottom: spacing.md,
//   },

//   infoText: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     marginBottom: spacing.sm,
//     lineHeight: 20,
//   },

//   bold: {
//     fontWeight: 'bold',
//     color: colors.text.primary,
//   },
// });
