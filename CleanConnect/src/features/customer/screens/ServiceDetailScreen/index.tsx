import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Image,
  Dimensions,
} from 'react-native';
import { router, useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { ServiceCard } from '../../../../components/common/ServiceCard';
import { useBookingStore } from '../../../../store/bookingStore';
import { TopNavbar } from '../../components/TopNavbar';
import { serviceDetailStyles } from './styles';
import {
  serviceCategories,
  getCategoryById,
  getServiceById,
  ServiceCategory,
  ServiceItem
} from '../../../../data/serviceCategories';

const { width } = Dimensions.get('window');

// Helper function to determine navigation type and get appropriate data
const getScreenData = (params: any) => {
  const { serviceId, categoryId } = params;

  if (categoryId) {
    // Navigation from category (e.g., from home screen)
    const category = getCategoryById(categoryId);
    return { type: 'category', data: category };
  } else if (serviceId) {
    // Navigation from specific service
    const service = getServiceById(serviceId);
    if (service) {
      // Find the category that contains this service
      const category = serviceCategories.find(cat =>
        cat.subcategories.some(sub =>
          sub.services.some(s => s.id === serviceId)
        )
      );
      return { type: 'service', data: category, selectedService: service };
    }
  }

  // Fallback to first category
  return { type: 'category', data: serviceCategories[0] };
};

export const ServiceDetailScreen: React.FC = () => {
  const params = useLocalSearchParams();
  const { setSelectedService } = useBookingStore();

  // Get screen data based on navigation type (category or service)
  const screenData = getScreenData(params);
  const serviceData = screenData.data as ServiceCategory;

  const [selectedSubcategory, setSelectedSubcategory] = useState(
    serviceData?.subcategories[0]?.id || ''
  );
  const [cartItems, setCartItems] = useState<{ [key: string]: number }>({});
  const [cartTotal, setCartTotal] = useState(0);

  const handleBack = () => {
    router.back();
  };

  const handleSearch = () => {
    router.push('/customer/search');
  };

  const handleShare = () => {
    // Handle share functionality
    console.log('Share service');
  };

  const handleServiceAdd = (serviceId: string) => {
    const service = serviceData.subcategories
      .flatMap(subcat => subcat.services)
      .find(s => s.id === serviceId);

    if (service) {
      const newCount = (cartItems[serviceId] || 0) + 1;
      const newCartItems = { ...cartItems, [serviceId]: newCount };
      setCartItems(newCartItems);

      // Calculate total
      const total = Object.entries(newCartItems).reduce((sum, [id, count]) => {
        const servicePrice = serviceData.subcategories
          .flatMap(subcat => subcat.services)
          .find(s => s.id === id)?.startingPrice || 0;
        return sum + (servicePrice * count);
      }, 0);
      setCartTotal(total);
    }
  };

  const handleServicePress = (serviceId: string) => {
    // Navigate to service customization or booking
    const service = serviceData.subcategories
      .flatMap(subcat => subcat.services)
      .find(s => s.id === serviceId);

    if (service) {
      setSelectedService({
        id: service.id,
        name: service.name,
        description: service.description,
        price: service.startingPrice,
        icon: service.image,
      });
      router.push('/customer/booking/date-time');
    }
  };

  const handleViewCart = () => {
    router.push('/customer/bookings');
  };

  const updateQuantity = (serviceId: string, change: number) => {
    const currentCount = cartItems[serviceId] || 0;
    const newCount = Math.max(0, currentCount + change);

    if (newCount === 0) {
      const { [serviceId]: removed, ...rest } = cartItems;
      setCartItems(rest);
    } else {
      setCartItems({ ...cartItems, [serviceId]: newCount });
    }

    // Recalculate total
    const newCartItems = newCount === 0
      ? { ...cartItems, [serviceId]: undefined }
      : { ...cartItems, [serviceId]: newCount };

    const total = Object.entries(newCartItems).reduce((sum, [id, count]) => {
      if (!count) return sum;
      const servicePrice = serviceData.subcategories
        .flatMap(subcat => subcat.services)
        .find(s => s.id === id)?.startingPrice || 0;
      return sum + (servicePrice * count);
    }, 0);
    setCartTotal(total);
  };

  const selectedSubcategoryData = serviceData.subcategories.find(subcat => subcat.id === selectedSubcategory);
  const totalCartItems = Object.values(cartItems).reduce((sum, count) => sum + count, 0);

  // Early return if no service data
  if (!serviceData) {
    return (
      <SafeAreaView style={serviceDetailStyles.container}>
        <View style={serviceDetailStyles.errorContainer}>
          <Text style={serviceDetailStyles.errorText}>Service not found</Text>
          <TouchableOpacity style={serviceDetailStyles.retryButton} onPress={() => router.back()}>
            <Text style={serviceDetailStyles.retryButtonText}>Go Back</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={serviceDetailStyles.container}>
      {/* Top Navigation */}
      <TopNavbar
        title={serviceData.name}
        showBack={true}
        showNotification={false}
        showCart={true}
        onBackPress={handleBack}
      />

      <ScrollView style={serviceDetailStyles.content} showsVerticalScrollIndicator={false}>
        {/* Service Title & Rating */}
        <View style={serviceDetailStyles.titleSection}>
          <Text style={serviceDetailStyles.serviceTitle}>{serviceData.name}</Text>
          <View style={serviceDetailStyles.ratingContainer}>
            <Ionicons name="star" size={16} color="#FFB800" />
            <Text style={serviceDetailStyles.ratingText}>
              {serviceData.rating} ({serviceData.totalBookings} bookings)
            </Text>
          </View>
        </View>

        {/* Promotion Banner */}
        {serviceData.promotions.map((promo) => (
          <View key={promo.id} style={serviceDetailStyles.promotionBanner}>
            <Text style={serviceDetailStyles.promoIcon}>{promo.icon}</Text>
            <View style={serviceDetailStyles.promoContent}>
              <Text style={serviceDetailStyles.promoTitle}>{promo.title}</Text>
              <Text style={serviceDetailStyles.promoSubtitle}>{promo.subtitle}</Text>
            </View>
          </View>
        ))}

        {/* Subcategory Images */}
        <View style={serviceDetailStyles.categoryImages}>
          {serviceData.images.map((image) => (
            <TouchableOpacity
              key={image.id}
              style={serviceDetailStyles.categoryImageContainer}
              onPress={() => setSelectedSubcategory(image.id)}
            >
              <View style={serviceDetailStyles.categoryImageWrapper}>
                <Text style={serviceDetailStyles.categoryImage}>{image.url}</Text>
              </View>
              <Text style={serviceDetailStyles.categoryImageTitle}>{image.title}</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Selected Subcategory Services */}
        {selectedSubcategoryData && (
          <View style={serviceDetailStyles.servicesSection}>
            <Text style={serviceDetailStyles.sectionTitle}>{selectedSubcategoryData.title}</Text>

            {selectedSubcategoryData.services.map((service) => (
              <View key={service.id} style={serviceDetailStyles.serviceItemContainer}>
                <ServiceCard
                  id={service.id}
                  name={service.name}
                  description={service.description}
                  price={service.startingPrice}
                  originalPrice={service.originalPrice}
                  duration={service.duration}
                  image={service.image}
                  note={`${service.options} options`}
                  popular={service.popular}
                  variant="modern"
                  onPress={handleServicePress}
                  onAddPress={handleServiceAdd}
                />

                {/* Quantity Controls */}
                {cartItems[service.id] && (
                  <View style={serviceDetailStyles.quantityControls}>
                    <TouchableOpacity
                      style={serviceDetailStyles.quantityButton}
                      onPress={() => updateQuantity(service.id, -1)}
                    >
                      <Text style={serviceDetailStyles.quantityButtonText}>−</Text>
                    </TouchableOpacity>
                    <Text style={serviceDetailStyles.quantityText}>{cartItems[service.id]}</Text>
                    <TouchableOpacity
                      style={serviceDetailStyles.quantityButton}
                      onPress={() => updateQuantity(service.id, 1)}
                    >
                      <Text style={serviceDetailStyles.quantityButtonText}>+</Text>
                    </TouchableOpacity>
                  </View>
                )}
              </View>
            ))}
          </View>
        )}
      </ScrollView>

      {/* Floating Menu Button */}
      <TouchableOpacity style={serviceDetailStyles.menuButton}>
        <Text style={serviceDetailStyles.menuButtonText}>Menu</Text>
      </TouchableOpacity>

      {/* Bottom Cart Bar */}
      {totalCartItems > 0 && (
        <View style={serviceDetailStyles.cartBar}>
          <Text style={serviceDetailStyles.cartTotal}>₹{cartTotal.toLocaleString()}</Text>
          <TouchableOpacity style={serviceDetailStyles.viewCartButton} onPress={handleViewCart}>
            <Text style={serviceDetailStyles.viewCartText}>View Cart</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};
