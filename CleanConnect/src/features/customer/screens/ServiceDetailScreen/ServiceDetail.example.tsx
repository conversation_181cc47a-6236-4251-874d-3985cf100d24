// import React from 'react';
// import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
// import { router } from 'expo-router';
// import { colors } from '@/constants/colors';
// import { spacing } from '@/constants/spacing';
// import { textStyles } from '@/constants/typography';

// /**
//  * Example component showing how to navigate to the Service Detail screen
//  * This demonstrates the integration points and navigation patterns
//  */
// export const ServiceDetailExample: React.FC = () => {
  
//   // Example service IDs that would come from your service data
//   const exampleServices = [
//     { id: '1', name: 'Full Home Cleaning', category: 'Cleaning' },
//     { id: '2', name: 'Deep Kitchen Clean', category: 'Kitchen' },
//     { id: '3', name: 'Bathroom Sanitization', category: 'Bathroom' },
//     { id: '4', name: 'Carpet & Upholstery', category: 'Specialized' },
//     { id: '5', name: 'Window Cleaning', category: 'Windows' },
//   ];

//   const handleServicePress = (serviceId: string, serviceName: string) => {
//     console.log(`Navigating to service detail for: ${serviceName}`);
//     router.push(`/customer/service-detail/${serviceId}`);
//   };

//   const handleBackToServices = () => {
//     router.push('/customer/booking/service-selection');
//   };

//   return (
//     <View style={styles.container}>
//       <Text style={styles.title}>Service Detail Navigation Example</Text>
//       <Text style={styles.subtitle}>
//         Tap any service below to see the Service Detail screen in action
//       </Text>

//       <View style={styles.servicesContainer}>
//         {exampleServices.map((service) => (
//           <TouchableOpacity
//             key={service.id}
//             style={styles.serviceButton}
//             onPress={() => handleServicePress(service.id, service.name)}
//             activeOpacity={0.8}
//           >
//             <View style={styles.serviceContent}>
//               <Text style={styles.serviceName}>{service.name}</Text>
//               <Text style={styles.serviceCategory}>{service.category}</Text>
//             </View>
//             <Text style={styles.arrow}>→</Text>
//           </TouchableOpacity>
//         ))}
//       </View>

//       <TouchableOpacity style={styles.backButton} onPress={handleBackToServices}>
//         <Text style={styles.backButtonText}>← Back to Service Selection</Text>
//       </TouchableOpacity>

//       <View style={styles.infoBox}>
//         <Text style={styles.infoTitle}>What you'll see in Service Detail:</Text>
//         <Text style={styles.infoText}>• Service overview with rating and bookings</Text>
//         <Text style={styles.infoText}>• Category selection with visual icons</Text>
//         <Text style={styles.infoText}>• Service cards with pricing and options</Text>
//         <Text style={styles.infoText}>• Add to cart functionality</Text>
//         <Text style={styles.infoText}>• Quantity controls and cart management</Text>
//         <Text style={styles.infoText}>• Promotion banners and special offers</Text>
//         <Text style={styles.infoText}>• Floating menu button</Text>
//         <Text style={styles.infoText}>• Seamless navigation to booking flow</Text>
//       </View>
//     </View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     flex: 1,
//     backgroundColor: colors.background.primary,
//     padding: spacing.layout.container,
//   },

//   title: {
//     ...textStyles.h2,
//     color: colors.text.primary,
//     textAlign: 'center',
//     marginBottom: spacing.md,
//   },

//   subtitle: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     textAlign: 'center',
//     marginBottom: spacing.xxl,
//   },

//   servicesContainer: {
//     marginBottom: spacing.xxl,
//   },

//   serviceButton: {
//     flexDirection: 'row',
//     alignItems: 'center',
//     justifyContent: 'space-between',
//     backgroundColor: colors.white,
//     padding: spacing.lg,
//     borderRadius: 12,
//     marginBottom: spacing.md,
//     shadowColor: colors.shadow.medium,
//     shadowOffset: { width: 0, height: 2 },
//     shadowOpacity: 0.1,
//     shadowRadius: 4,
//     elevation: 2,
//     borderWidth: 1,
//     borderColor: colors.border.light,
//   },

//   serviceContent: {
//     flex: 1,
//   },

//   serviceName: {
//     ...textStyles.labelLarge,
//     color: colors.text.primary,
//     marginBottom: spacing.xs,
//   },

//   serviceCategory: {
//     ...textStyles.caption,
//     color: colors.text.tertiary,
//   },

//   arrow: {
//     ...textStyles.h3,
//     color: colors.primary,
//   },

//   backButton: {
//     backgroundColor: colors.gray[100],
//     padding: spacing.lg,
//     borderRadius: 12,
//     alignItems: 'center',
//     marginBottom: spacing.xxl,
//   },

//   backButtonText: {
//     ...textStyles.button,
//     color: colors.text.primary,
//   },

//   infoBox: {
//     backgroundColor: colors.primary + '10',
//     padding: spacing.lg,
//     borderRadius: 12,
//     borderWidth: 1,
//     borderColor: colors.primary + '20',
//   },

//   infoTitle: {
//     ...textStyles.labelLarge,
//     color: colors.primary,
//     marginBottom: spacing.md,
//   },

//   infoText: {
//     ...textStyles.body,
//     color: colors.text.secondary,
//     marginBottom: spacing.xs,
//   },
// });

// /**
//  * Usage Instructions:
//  * 
//  * 1. Import this component in your app
//  * 2. Use it as a testing/demo screen
//  * 3. Navigate to it from your main navigation
//  * 4. Tap any service to see the Service Detail screen
//  * 
//  * Integration Example:
//  * 
//  * // In your navigation or test screen
//  * import { ServiceDetailExample } from './ServiceDetail.example';
//  * 
//  * // Render the example
//  * <ServiceDetailExample />
//  * 
//  * Navigation Flow:
//  * 1. User taps a service in this example
//  * 2. App navigates to /customer/service-detail/[serviceId]
//  * 3. Service Detail screen loads with mock data
//  * 4. User can explore categories, add to cart, etc.
//  * 5. User can proceed to booking or return to services
//  */
