import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography, textStyles } from '@/constants/typography';

const { width } = Dimensions.get('window');

export const serviceDetailStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.primary,
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.layout.container,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },

  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  },

  headerTitle: {
    ...textStyles.h4,
    color: colors.text.primary,
    flex: 1,
    textAlign: 'center',
    marginHorizontal: spacing.md,
  },

  headerActions: {
    flexDirection: 'row',
    gap: spacing.sm,
  },

  // Content Styles
  content: {
    flex: 1,
  },

  titleSection: {
    paddingHorizontal: spacing.layout.container,
    paddingVertical: spacing.lg,
    backgroundColor: colors.white,
  },

  serviceTitle: {
    ...textStyles.h2,
    color: colors.text.primary,
    marginBottom: spacing.sm,
  },

  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  },

  ratingText: {
    ...textStyles.body,
    color: colors.text.secondary,
  },

  // Promotion Banner Styles
  promotionBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary + '10',
    marginHorizontal: spacing.layout.container,
    marginVertical: spacing.md,
    padding: spacing.lg,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: colors.primary + '20',
  },

  promoIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },

  promoContent: {
    flex: 1,
  },

  promoTitle: {
    ...textStyles.labelLarge,
    color: colors.primary,
    marginBottom: spacing.xs,
  },

  promoSubtitle: {
    ...textStyles.body,
    color: colors.text.secondary,
  },

  // Category Images Styles
  categoryImages: {
    flexDirection: 'row',
    paddingHorizontal: spacing.layout.container,
    paddingVertical: spacing.lg,
    gap: spacing.md,
    backgroundColor: colors.white,
    marginTop: spacing.sm,
  },

  categoryImageContainer: {
    flex: 1,
    alignItems: 'center',
  },

  categoryImageWrapper: {
    width: 60,
    height: 60,
    borderRadius: 12,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.border.light,
  },

  categoryImage: {
    fontSize: 28,
  },

  categoryImageTitle: {
    ...textStyles.caption,
    color: colors.text.secondary,
    textAlign: 'center',
    lineHeight: typography.sizes.xs * typography.lineHeights.tight,
  },

  // Services Section Styles
  servicesSection: {
    backgroundColor: colors.white,
    marginTop: spacing.sm,
    paddingTop: spacing.lg,
  },

  sectionTitle: {
    ...textStyles.h3,
    color: colors.text.primary,
    paddingHorizontal: spacing.layout.container,
    marginBottom: spacing.lg,
  },

  serviceItemContainer: {
    paddingHorizontal: spacing.layout.container,
    position: 'relative',
  },

  // Quantity Controls Styles
  quantityControls: {
    position: 'absolute',
    bottom: spacing.lg + 60, // Position above the add button
    right: spacing.layout.container + spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderRadius: 20,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    borderWidth: 1,
    borderColor: colors.border.light,
  },

  quantityButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },

  quantityButtonText: {
    ...textStyles.button,
    color: colors.white,
    fontSize: typography.sizes.lg,
    lineHeight: typography.sizes.lg,
  },

  quantityText: {
    ...textStyles.labelLarge,
    color: colors.text.primary,
    marginHorizontal: spacing.md,
    minWidth: 24,
    textAlign: 'center',
  },

  // Cart Bar Styles
  cartBar: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    paddingHorizontal: spacing.layout.container,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.border.light,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },

  cartTotal: {
    ...textStyles.h3,
    color: colors.text.primary,
  },

  viewCartButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xxl,
    paddingVertical: spacing.lg,
    borderRadius: 12,
    minWidth: 120,
    alignItems: 'center',
    justifyContent: 'center',
  },

  viewCartText: {
    ...textStyles.button,
    color: colors.white,
  },

  // Additional Utility Styles
  divider: {
    height: 1,
    backgroundColor: colors.border.light,
    marginVertical: spacing.md,
  },

  loadingContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background.primary,
  },

  errorContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: colors.background.primary,
    paddingHorizontal: spacing.layout.container,
  },

  errorText: {
    ...textStyles.body,
    color: colors.error,
    textAlign: 'center',
    marginBottom: spacing.lg,
  },

  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 8,
  },

  retryButtonText: {
    ...textStyles.button,
    color: colors.white,
  },

  // Menu Button (floating action button style)
  menuButton: {
    position: 'absolute',
    bottom: spacing.xxl,
    left: '50%',
    transform: [{ translateX: -30 }],
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.text.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.shadow.dark,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
    zIndex: 1000,
  },

  menuButtonText: {
    ...textStyles.button,
    color: colors.white,
    fontSize: typography.sizes.sm,
  },
});
