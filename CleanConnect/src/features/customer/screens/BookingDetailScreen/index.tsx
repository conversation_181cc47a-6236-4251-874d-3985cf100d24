import React from 'react';
import { View, Text, TouchableOpacity, ScrollView } from 'react-native';
import { colors } from '../../../../constants/colors';
import { bookingDetailScreenStyles, statusColors as styleStatusColors, BookingStatus } from '../../styles/BookingDetailScreen.styles';

const booking: {
  id: string;
  type: string;
  date: string;
  status: BookingStatus;
  price: number;
  address: string;
  details: string;
} = {
  id: '1',
  type: 'Total Home Cleaning',
  date: '2024-06-10',
  status: 'Confirmed',
  price: 99,
  address: '123 Main St, City, Country',
  details: 'Includes kitchen, bathroom, and living room cleaning. Special request: Use eco-friendly products.',
};



const BookingDetailScreen = () => {
  return (
    <ScrollView style={bookingDetailScreenStyles.container}>
      <Text style={bookingDetailScreenStyles.header}>Booking Details</Text>
      <View style={bookingDetailScreenStyles.card}>
        <View style={bookingDetailScreenStyles.cardHeader}>
          <Text style={bookingDetailScreenStyles.type}>{booking.type}</Text>
          <Text style={[bookingDetailScreenStyles.status, { color: styleStatusColors[booking.status] || colors.gray[600] }]}>
            {booking.status}
          </Text>
        </View>
        <Text style={bookingDetailScreenStyles.date}>Date: {booking.date}</Text>
        <Text style={bookingDetailScreenStyles.price}>${booking.price}</Text>
        <Text style={bookingDetailScreenStyles.address}>Address: {booking.address}</Text>
        <Text style={bookingDetailScreenStyles.details}>{booking.details}</Text>
      </View>
      <View style={bookingDetailScreenStyles.summaryCard}>
        <Text style={bookingDetailScreenStyles.summaryTitle}>Summary</Text>
        <Text style={bookingDetailScreenStyles.summaryText}>Service: {booking.type}</Text>
        <Text style={bookingDetailScreenStyles.summaryText}>Status: {booking.status}</Text>
        <Text style={bookingDetailScreenStyles.summaryText}>Total: ${booking.price}</Text>
      </View>
      <TouchableOpacity style={bookingDetailScreenStyles.button}>
        <Text style={bookingDetailScreenStyles.buttonText}>Contact Support</Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

export default BookingDetailScreen;