import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { colors } from '@/constants/colors';
import { Button } from '../../../../components/common/Button';
import { TopNavbar } from '../../components/TopNavbar';
import { useNotificationStore } from '../../../../store/useNotificationStore';
import { notificationCenterScreenStyles } from '../../styles/NotificationCenterScreen.styles';

// Mock notifications for demonstration
const mockNotifications = [
  {
    id: '1',
    type: 'booking_update',
    title: 'Booking Confirmed',
    message: 'Your house cleaning booking has been confirmed by <PERSON>',
    read: false,
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    type: 'message',
    title: 'New Message',
    message: '<PERSON> sent you a message about your upcoming cleaning service',
    read: false,
    createdAt: '2024-01-15T09:15:00Z',
  },
  {
    id: '3',
    type: 'payment',
    title: 'Payment Successful',
    message: 'Your payment of $50 has been processed successfully',
    read: true,
    createdAt: '2024-01-14T16:45:00Z',
  },
  {
    id: '4',
    type: 'reminder',
    title: 'Upcoming Booking',
    message: 'Reminder: Your cleaning service is scheduled for tomorrow at 10:00 AM',
    read: true,
    createdAt: '2024-01-14T14:20:00Z',
  },
  {
    id: '5',
    type: 'promotion',
    title: 'Special Offer',
    message: 'Get 20% off on your next deep cleaning service! Limited time offer.',
    read: true,
    createdAt: '2024-01-13T11:30:00Z',
  },
];

export const NotificationCenterScreen: React.FC = () => {
  const notificationStore = useNotificationStore();
  const [selectedFilter, setSelectedFilter] = useState<'all' | 'unread'>('all');

  // Safely destructure store functions with fallbacks
  const {
    notifications = [],
    markAsRead = () => {},
    markAllAsRead = () => {},
    clearNotifications = () => {}
  } = notificationStore || {};

  // Use mock notifications for demo, in real app use notifications from store
  const displayNotifications = notifications.length > 0 ? notifications : mockNotifications;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'booking_update':
        return '📋';
      case 'message':
        return '💬';
      case 'payment':
        return '💳';
      case 'reminder':
        return '⏰';
      case 'promotion':
        return '🎉';
      default:
        return '📢';
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'booking_update':
        return colors.primary;
      case 'message':
        return colors.secondary;
      case 'payment':
        return colors.success;
      case 'reminder':
        return colors.warning;
      case 'promotion':
        return colors.accent;
      default:
        return colors.gray[500];
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const handleNotificationPress = (notification: any) => {
    if (!notification.read && markAsRead) {
      markAsRead(notification.id);
    }
    // Navigate based on notification type
    console.log('Navigate based on notification:', notification.type);
  };

  const handleMarkAllAsRead = () => {
    if (!markAllAsRead) return;

    Alert.alert(
      'Mark All as Read',
      'Are you sure you want to mark all notifications as read?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Mark All Read',
          onPress: () => {
            markAllAsRead();
          },
        },
      ]
    );
  };

  const handleClearAll = () => {
    if (!clearNotifications) return;

    Alert.alert(
      'Clear All Notifications',
      'Are you sure you want to clear all notifications? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Clear All',
          style: 'destructive',
          onPress: () => {
            clearNotifications();
          },
        },
      ]
    );
  };

  const filteredNotifications = selectedFilter === 'all' 
    ? displayNotifications 
    : displayNotifications.filter(n => !n.read);

  return (
    <View style={notificationCenterScreenStyles.container}>
      {/* Top Navigation */}
      <TopNavbar
        title="Notifications"
        showBack={true}
        showCart={true}
      />

      {/* Notification Count */}
      <View style={notificationCenterScreenStyles.countContainer}>
        <Text style={notificationCenterScreenStyles.headerSubtitle}>
          {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''}
        </Text>
      </View>

      {/* Filter Tabs */}
      <View style={notificationCenterScreenStyles.filterContainer}>
        <TouchableOpacity
          style={[
            notificationCenterScreenStyles.filterTab,
            selectedFilter === 'all' && notificationCenterScreenStyles.filterTabActive,
          ]}
          onPress={() => setSelectedFilter('all')}
        >
          <Text style={[
            notificationCenterScreenStyles.filterTabText,
            selectedFilter === 'all' && notificationCenterScreenStyles.filterTabTextActive,
          ]}>
            All
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[
            notificationCenterScreenStyles.filterTab,
            selectedFilter === 'unread' && notificationCenterScreenStyles.filterTabActive,
          ]}
          onPress={() => setSelectedFilter('unread')}
        >
          <Text style={[
            notificationCenterScreenStyles.filterTabText,
            selectedFilter === 'unread' && notificationCenterScreenStyles.filterTabTextActive,
          ]}>
            Unread
          </Text>
        </TouchableOpacity>
      </View>

      {/* Action Buttons */}
      {filteredNotifications.length > 0 && (
        <View style={notificationCenterScreenStyles.actionButtons}>
          <Button
            title="Mark All Read"
            onPress={handleMarkAllAsRead}
            size="small"
            style={notificationCenterScreenStyles.actionButton}
          />
          <Button
            title="Clear All"
            onPress={handleClearAll}
            size="small"
            style={[notificationCenterScreenStyles.actionButton, notificationCenterScreenStyles.clearButton]}
          />
        </View>
      )}

      <ScrollView style={notificationCenterScreenStyles.content} showsVerticalScrollIndicator={false}>
        {filteredNotifications.length === 0 ? (
          <View style={notificationCenterScreenStyles.emptyState}>
            <Text style={notificationCenterScreenStyles.emptyStateIcon}>🔔</Text>
            <Text style={notificationCenterScreenStyles.emptyStateTitle}>No notifications</Text>
            <Text style={notificationCenterScreenStyles.emptyStateDescription}>
              {selectedFilter === 'all' 
                ? 'You\'re all caught up! No notifications to show.'
                : 'No unread notifications at the moment.'
              }
            </Text>
          </View>
        ) : (
          filteredNotifications.map((notification) => (
            <TouchableOpacity
              key={notification.id}
              style={[
                notificationCenterScreenStyles.notificationCard,
                !notification.read && notificationCenterScreenStyles.unreadNotification,
              ]}
              onPress={() => handleNotificationPress(notification)}
            >
              <View style={notificationCenterScreenStyles.notificationHeader}>
                <View style={notificationCenterScreenStyles.notificationIcon}>
                  <Text style={notificationCenterScreenStyles.notificationIconText}>
                    {getNotificationIcon(notification.type)}
                  </Text>
                </View>
                <View style={notificationCenterScreenStyles.notificationContent}>
                  <Text style={notificationCenterScreenStyles.notificationTitle}>{notification.title}</Text>
                  <Text style={notificationCenterScreenStyles.notificationMessage}>{notification.message}</Text>
                  <Text style={notificationCenterScreenStyles.notificationTime}>
                    {formatDate(notification.createdAt)}
                  </Text>
                </View>
                {!notification.read && (
                  <View style={[
                    notificationCenterScreenStyles.unreadIndicator,
                    { backgroundColor: getNotificationColor(notification.type) }
                  ]} />
                )}
              </View>
            </TouchableOpacity>
          ))
        )}
        
        {/* Bottom Spacing */}
        <View style={notificationCenterScreenStyles.bottomSpacing} />
      </ScrollView>
    </View>
  );
};
