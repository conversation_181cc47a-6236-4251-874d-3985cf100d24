import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { colors } from '@/constants/colors';
import { useNotificationStore } from '../../../../store/useNotificationStore';
import { chatScreenStyles } from '../../styles/ChatScreen.styles';


// Mock chat data
const mockMessages = [
  {
    id: '1',
    text: 'Hi! I\'m on my way to your location. Should be there in 10 minutes.',
    sender: 'provider',
    timestamp: '10:00 AM',
    status: 'sent',
  },
  {
    id: '2',
    text: 'Great! I\'ll be waiting. Please ring the doorbell when you arrive.',
    sender: 'customer',
    timestamp: '10:01 AM',
    status: 'sent',
  },
  {
    id: '3',
    text: 'I\'ve arrived at your location. Ringing the doorbell now.',
    sender: 'provider',
    timestamp: '10:10 AM',
    status: 'sent',
  },
  {
    id: '4',
    text: 'Perfect! I\'m coming to the door.',
    sender: 'customer',
    timestamp: '10:10 AM',
    status: 'sent',
  },
  {
    id: '5',
    text: 'I\'m starting with the kitchen cleaning as you requested. Everything looks good so far.',
    sender: 'provider',
    timestamp: '10:15 AM',
    status: 'sent',
  },
];

const providerInfo = {
  name: 'John Doe',
  avatar: '👨‍💼',
  rating: 4.8,
  service: 'House Cleaning',
  status: 'online',
};

export const ChatScreen: React.FC = () => {
  const [messages, setMessages] = useState(mockMessages);
  const [newMessage, setNewMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);
  const { subscribeToChatMessages, simulateNewMessage } = useNotificationStore();

  useEffect(() => {
    // Subscribe to real-time chat messages
    const bookingId = '1'; // In real app, get from route params
    subscribeToChatMessages(bookingId, (message) => {
      console.log('New message received:', message);
      const newMsg = {
        id: message.id,
        text: message.message,
        sender: message.senderType === 'customer' ? 'customer' : 'provider',
        timestamp: new Date(message.timestamp).toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        }),
        status: 'sent',
      };
      setMessages(prev => [...prev, newMsg]);
    });

    // Simulate some real-time messages for demo
    setTimeout(() => {
      simulateNewMessage(bookingId, {
        id: Date.now().toString(),
        bookingId: bookingId,
        senderId: 'provider-123',
        senderType: 'provider',
        message: 'I\'m about 5 minutes away from your location. Should I call when I arrive?',
        timestamp: new Date().toISOString(),
      });
    }, 8000);

    return () => {
      // Cleanup subscriptions when component unmounts
      // This will be handled by the store
    };
  }, []);

  useEffect(() => {
    // Scroll to bottom when new messages arrive
    setTimeout(() => {
      scrollViewRef.current?.scrollToEnd({ animated: true });
    }, 100);
  }, [messages]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      const message = {
        id: Date.now().toString(),
        text: newMessage.trim(),
        sender: 'customer',
        timestamp: new Date().toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true,
        }),
        status: 'sending',
      };

      setMessages(prev => [...prev, message]);
      setNewMessage('');

      // Simulate message being sent
      setTimeout(() => {
        setMessages(prev =>
          prev.map(msg =>
            msg.id === message.id ? { ...msg, status: 'sent' } : msg
          )
        );
      }, 1000);

      // Simulate provider typing and response
      setTimeout(() => {
        setIsTyping(true);
      }, 2000);

      setTimeout(() => {
        setIsTyping(false);
        const response = {
          id: (Date.now() + 1).toString(),
          text: 'Got it! I\'ll make sure to follow your instructions.',
          sender: 'provider',
          timestamp: new Date().toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true,
          }),
          status: 'sent',
        };
        setMessages(prev => [...prev, response]);
      }, 4000);
    }
  };

  const renderMessage = (message: any) => {
    const isCustomer = message.sender === 'customer';
    
    return (
      <View
        key={message.id}
        style={[
          chatScreenStyles.messageContainer,
          isCustomer ? chatScreenStyles.customerMessage : chatScreenStyles.providerMessage,
        ]}
      >
        <View
          style={[
            chatScreenStyles.messageBubble,
            isCustomer ? chatScreenStyles.customerBubble : chatScreenStyles.providerBubble,
          ]}
        >
          <Text
            style={[
              chatScreenStyles.messageText,
              isCustomer ? chatScreenStyles.customerText : chatScreenStyles.providerText,
            ]}
          >
            {message.text}
          </Text>
          <View style={chatScreenStyles.messageMeta}>
            <Text style={chatScreenStyles.messageTime}>{message.timestamp}</Text>
            {isCustomer && (
              <Text style={chatScreenStyles.messageStatus}>
                {message.status === 'sending' ? '⏳' : '✓'}
              </Text>
            )}
          </View>
        </View>
      </View>
    );
  };

  return (
    <SafeAreaView style={chatScreenStyles.container}>
      {/* Header */}
      <View style={chatScreenStyles.header}>
        <View style={chatScreenStyles.providerInfo}>
          <Text style={chatScreenStyles.providerAvatar}>{providerInfo.avatar}</Text>
          <View style={chatScreenStyles.providerDetails}>
            <Text style={chatScreenStyles.providerName}>{providerInfo.name}</Text>
            <Text style={chatScreenStyles.providerService}>{providerInfo.service}</Text>
            <View style={chatScreenStyles.statusContainer}>
              <View style={[
                chatScreenStyles.statusDot,
                { backgroundColor: providerInfo.status === 'online' ? colors.success : colors.gray[400] }
              ]} />
              <Text style={chatScreenStyles.statusText}>
                {providerInfo.status === 'online' ? 'Online' : 'Offline'}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Messages */}
      <KeyboardAvoidingView
        style={chatScreenStyles.chatContainer}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
      >
        <ScrollView
          ref={scrollViewRef}
          style={chatScreenStyles.messagesContainer}
          showsVerticalScrollIndicator={false}
        >
          {messages.map(renderMessage)}
          
          {/* Typing Indicator */}
          {isTyping && (
            <View style={chatScreenStyles.typingContainer}>
              <View style={chatScreenStyles.typingBubble}>
                <Text style={chatScreenStyles.typingText}>Provider is typing...</Text>
                <View style={chatScreenStyles.typingDots}>
                  <View style={[chatScreenStyles.dot, chatScreenStyles.dot1]} />
                  <View style={[chatScreenStyles.dot, chatScreenStyles.dot2]} />
                  <View style={[chatScreenStyles.dot, chatScreenStyles.dot3]} />
                </View>
              </View>
            </View>
          )}
        </ScrollView>

        {/* Message Input */}
        <View style={chatScreenStyles.inputContainer}>
          <TextInput
            style={chatScreenStyles.textInput}
            value={newMessage}
            onChangeText={setNewMessage}
            placeholder="Type a message..."
            placeholderTextColor={colors.gray[400]}
            multiline
            maxLength={500}
          />
          <TouchableOpacity
            style={[
              chatScreenStyles.sendButton,
              !newMessage.trim() && chatScreenStyles.sendButtonDisabled,
            ]}
            onPress={handleSendMessage}
            disabled={!newMessage.trim()}
          >
            <Text style={chatScreenStyles.sendButtonText}>📤</Text>
          </TouchableOpacity>
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};
