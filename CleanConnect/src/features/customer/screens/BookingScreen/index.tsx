import React, { useState } from 'react';
import { View, Text, ScrollView, TouchableOpacity, SafeAreaView } from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/colors';
import { TopNavbar } from '../../components/TopNavbar';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { bookingScreenStyles, statusColors, BookingStatus } from '../../styles/BookingScreen.styles';

interface Booking {
  id: string;
  type: string;
  date: string;
  status: BookingStatus;
  price: number;
}

// Mock bookings data - set to empty array to show empty state
const bookings: Booking[] = [
  // Uncomment below to test with bookings
  {
    id: '1',
    type: 'Total Home Cleaning',
    date: '2024-06-10',
    status: 'Confirmed',
    price: 99,
  },
  {
    id: '2',
    type: 'Restroom Cleaning',
    date: '2024-06-12',
    status: 'Pending',
    price: 49,
  },
];

export const BookingScreen: React.FC = () => {
  const [showHelp, setShowHelp] = useState(false);

  const handleBookingPress = (bookingId: string) => {
    router.push(`/customer/booking-detail/${bookingId}` as any);
  };

  const handleHelpPress = () => {
    setShowHelp(true);
  };

  const handleBackFromHelp = () => {
    setShowHelp(false);
  };

  const handleExploreServices = () => {
    router.push('/customer/booking/service-selection');
  };

  // Help Screen Component
  const HelpScreen = () => (
    <SafeAreaView style={bookingScreenStyles.container}>
      <TopNavbar
        title="Help"
        showBack={true}
        onBackPress={handleBackFromHelp}
        iconColor={colors.text.primary}
      />

      <ScrollView style={bookingScreenStyles.helpContent}>
        <Text style={bookingScreenStyles.helpSectionTitle}>All topics</Text>

        <View style={bookingScreenStyles.helpTopicsList}>
          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="person-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Account</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="play-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Getting started with CleanConnect</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="card-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Payment & Credits</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="star-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Plus Membership</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="shield-checkmark-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Safety</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>

          <TouchableOpacity style={bookingScreenStyles.helpTopic}>
            <View style={bookingScreenStyles.helpTopicContent}>
              <Ionicons name="document-text-outline" size={24} color={colors.text.secondary} />
              <Text style={bookingScreenStyles.helpTopicText}>Claim Warranty</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.text.tertiary} />
          </TouchableOpacity>
        </View>
      </ScrollView>

      <CustomerNavigation />
    </SafeAreaView>
  );

  // Main Booking Screen
  if (showHelp) {
    return <HelpScreen />;
  }

  return (
    <SafeAreaView style={bookingScreenStyles.container}>
      {/* Header with Title and Help Button */}
      <View style={bookingScreenStyles.headerContainer}>
        <Text style={bookingScreenStyles.header}>My Bookings</Text>
        <TouchableOpacity
          style={bookingScreenStyles.helpButton}
          onPress={handleHelpPress}
        >
          <Text style={bookingScreenStyles.helpButtonText}>Help</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={bookingScreenStyles.content} showsVerticalScrollIndicator={false}>
        {bookings.length === 0 ? (
          // Empty State
          <View style={bookingScreenStyles.emptyStateContainer}>
            <View style={bookingScreenStyles.emptyStateContent}>
              <Text style={bookingScreenStyles.emptyStateTitle}>No bookings yet.</Text>
              <Text style={bookingScreenStyles.emptyStateDescription}>
                Looks like you haven&apos;t experienced quality{'\n'}services at home.
              </Text>
              <TouchableOpacity
                style={bookingScreenStyles.exploreButton}
                onPress={handleExploreServices}
              >
                <Text style={bookingScreenStyles.exploreButtonText}>
                  Explore our services
                </Text>
                <Ionicons name="arrow-forward" size={16} color={colors.primary} />
              </TouchableOpacity>
            </View>
          </View>
        ) : (
          // Bookings List
          bookings.map((booking) => (
            <View key={booking.id} style={bookingScreenStyles.card}>
              <View style={bookingScreenStyles.cardHeader}>
                <Text style={bookingScreenStyles.type}>{booking.type}</Text>
                <Text style={[bookingScreenStyles.status, { color: statusColors[booking.status] || colors.gray[600] }]}>
                  {booking.status}
                </Text>
              </View>
              <Text style={bookingScreenStyles.date}>Date: {booking.date}</Text>
              <Text style={bookingScreenStyles.price}>${booking.price}</Text>
              <TouchableOpacity
                style={bookingScreenStyles.button}
                onPress={() => handleBookingPress(booking.id)}
              >
                <Text style={bookingScreenStyles.buttonText}>View Details</Text>
              </TouchableOpacity>
            </View>
          ))
        )}
      </ScrollView>

      <CustomerNavigation />
    </SafeAreaView>
  );
};

