import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  ScrollView,
  Image,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useBookingStore } from '../../../../store/bookingStore';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { styles } from './styles';

const hourOptions = [2, 3, 4, 5, 6, 7];
const cleanerOptions = [1, 2, 3, 4, 5, 6];

export function ConfirmReviewScreen() {
  const {
    selectedService,
    hours,
    cleaners,
    needsMaterials,
    total,
    setHours,
    setCleaners,
    setNeedsMaterials,
  } = useBookingStore();

  const handleConfirm = () => {
    router.push('/customer/booking/payment');
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="chevron-back" size={24} color="#000" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Confirm & Review</Text>
        <Text style={styles.stepIndicator}>Step 1 of 3</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Hours Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>How many hours?</Text>
          <View style={styles.optionsContainer}>
            {hourOptions.map((hour) => (
              <TouchableOpacity
                key={hour}
                style={[
                  styles.optionButton,
                  hours === hour && styles.optionButtonSelected,
                ]}
                onPress={() => setHours(hour)}
              >
                <Text
                  style={[
                    styles.optionText,
                    hours === hour && styles.optionTextSelected,
                  ]}
                >
                  {hour}h
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Cleaners Selection */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>How many cleaners?</Text>
          <View style={styles.optionsContainer}>
            {cleanerOptions.map((cleaner) => (
              <TouchableOpacity
                key={cleaner}
                style={[
                  styles.cleanerButton,
                  cleaners === cleaner && styles.cleanerButtonSelected,
                ]}
                onPress={() => setCleaners(cleaner)}
              >
                <Text
                  style={[
                    styles.cleanerText,
                    cleaners === cleaner && styles.cleanerTextSelected,
                  ]}
                >
                  {cleaner}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        {/* Materials Selection */}
        <View style={styles.section}>
          <View style={styles.materialsHeader}>
            <Text style={styles.sectionTitle}>Do you need cleaning materials?</Text>
            <TouchableOpacity style={styles.infoButton}>
              <Ionicons name="information-circle-outline" size={20} color="#666" />
            </TouchableOpacity>
          </View>
          <View style={styles.materialsContainer}>
            <TouchableOpacity
              style={[
                styles.materialButton,
                needsMaterials && styles.materialButtonSelected,
              ]}
              onPress={() => setNeedsMaterials(true)}
            >
              <Text
                style={[
                  styles.materialText,
                  needsMaterials && styles.materialTextSelected,
                ]}
              >
                Yes
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.materialButton,
                !needsMaterials && styles.materialButtonSelected,
              ]}
              onPress={() => setNeedsMaterials(false)}
            >
              <Text
                style={[
                  styles.materialText,
                  !needsMaterials && styles.materialTextSelected,
                ]}
              >
                No
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Service Summary */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Your Items</Text>
          {selectedService && (
            <View style={styles.serviceCard}>
              <Image
                source={{
                  uri: selectedService.image || 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?w=100&h=100&fit=crop',
                }}
                style={styles.serviceImage}
              />
              <View style={styles.serviceInfo}>
                <Text style={styles.serviceName}>
                  Luxurious Home Cleaning Mani-Pedi Bliss Awaits
                </Text>
                <Text style={styles.servicePrice}>
                  Price <Text style={styles.priceAmount}>${selectedService.price}.00</Text>
                </Text>
              </View>
            </View>
          )}
        </View>

        <View style={styles.spacer} />
      </ScrollView>

      {/* Footer */}
      <View style={styles.footer}>
        <View style={styles.priceContainer}>
          <Text style={styles.priceLabel}>Start from</Text>
          <Text style={styles.priceAmount}>${total.toFixed(2)}</Text>
        </View>
        <TouchableOpacity style={styles.confirmButton} onPress={handleConfirm}>
          <Text style={styles.confirmButtonText}>Confirm</Text>
        </TouchableOpacity>
      </View>

      <CustomerNavigation />
    </View>
  );
}
