import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { typography } from '@/constants/typography';
import { spacing } from '@/constants/spacing';

export const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.gray[50],
  } as ViewStyle,

  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  headerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  stepIndicator: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
  } as ViewStyle,

  section: {
    marginBottom: spacing.xl,
  } as ViewStyle,

  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
  } as TextStyle,

  optionsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: spacing.sm,
  } as ViewStyle,

  optionButton: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 12,
    backgroundColor: colors.gray[100],
    minWidth: 60,
    alignItems: 'center',
  } as ViewStyle,

  optionButtonSelected: {
    backgroundColor: colors.gray[900],
  } as ViewStyle,

  optionText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  optionTextSelected: {
    color: colors.white,
  } as TextStyle,

  cleanerButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  } as ViewStyle,

  cleanerButtonSelected: {
    backgroundColor: colors.primary,
    borderWidth: 3,
    borderColor: colors.primary + '40',
  } as ViewStyle,

  cleanerText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  cleanerTextSelected: {
    color: colors.white,
  } as TextStyle,

  materialsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.md,
  } as ViewStyle,

  infoButton: {
    marginLeft: spacing.sm,
  } as ViewStyle,

  materialsContainer: {
    flexDirection: 'row',
    gap: spacing.md,
  } as ViewStyle,

  materialButton: {
    flex: 1,
    paddingVertical: spacing.lg,
    borderRadius: 12,
    backgroundColor: colors.gray[400],
    alignItems: 'center',
  } as ViewStyle,

  materialButtonSelected: {
    backgroundColor: colors.gray[900],
  } as ViewStyle,

  materialText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  materialTextSelected: {
    color: colors.white,
  } as TextStyle,

  serviceCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
  } as ViewStyle,

  serviceImage: {
    width: 80,
    height: 80,
    borderRadius: 12,
    marginRight: spacing.md,
  } as ViewStyle,

  serviceInfo: {
    flex: 1,
  } as ViewStyle,

  serviceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.sm,
    lineHeight: typography.lineHeights.tight,
  } as TextStyle,

  servicePrice: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  priceAmount: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  spacer: {
    height: 100,
  } as ViewStyle,

  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 8,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  } as ViewStyle,

  priceContainer: {
    flex: 1,
  } as ViewStyle,

  priceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
    marginBottom: 2,
  } as TextStyle,

  confirmButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 16,
    minWidth: 120,
    alignItems: 'center',
  } as ViewStyle,

  confirmButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,
});
