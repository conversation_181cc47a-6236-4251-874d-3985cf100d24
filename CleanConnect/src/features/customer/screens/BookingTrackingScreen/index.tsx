import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Linking,
  RefreshControl,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { colors } from '@/constants/colors';
import { TopNavbar } from '../../components/TopNavbar';
import { CustomerNavigation } from '../../components/CustomerNavigation';
import { Button } from '../../../../components/common/Button';
import { useNotificationStore } from '../../../../store/useNotificationStore';
import { bookingTrackingScreenStyles } from '../../styles/BookingTrackingScreen.styles';




export const BookingTrackingScreen: React.FC = () => {
  const [refreshing, setRefreshing] = useState(false);
  const { addNotification } = useNotificationStore();

  // Simplified booking data for better UX
  const currentBooking = {
    id: '1',
    service: 'Deep House Cleaning',
    date: 'Today, Dec 15',
    time: '2:00 PM - 4:00 PM',
    status: 'IN_PROGRESS',
    progress: 75,
    estimatedCompletion: '3:45 PM',
    address: '123 Main Street, Apt 4B, New York, NY 10001',
    amount: 120,
    provider: {
      name: 'Sarah Johnson',
      phone: '+****************',
      rating: 4.8,
      avatar: 'SJ',
      photo: null,
    },
    specialInstructions: 'Please focus on kitchen and bathrooms. Use eco-friendly products only.',
    timeline: [
      { status: 'CONFIRMED', label: 'Booking Confirmed', time: '10:00 AM', completed: true },
      { status: 'ASSIGNED', label: 'Provider Assigned', time: '10:30 AM', completed: true },
      { status: 'ON_WAY', label: 'Provider On The Way', time: '1:30 PM', completed: true },
      { status: 'IN_PROGRESS', label: 'Service In Progress', time: '2:00 PM', completed: true },
      { status: 'COMPLETED', label: 'Service Completed', time: '', completed: false },
    ],
    recentUpdates: [
      {
        id: '1',
        message: 'Started cleaning the kitchen as requested',
        timestamp: '2:15 PM',
        type: 'progress',
      },
      {
        id: '2',
        message: 'Kitchen cleaning completed, moving to bathrooms',
        timestamp: '2:45 PM',
        type: 'progress',
      },
      {
        id: '3',
        message: 'Bathroom cleaning in progress',
        timestamp: '3:10 PM',
        type: 'progress',
      },
    ],
  };

  const onRefresh = React.useCallback(() => {
    setRefreshing(true);
    // Simulate refresh
    setTimeout(() => {
      setRefreshing(false);
      addNotification({
        id: Date.now().toString(),
        title: 'Booking Updated',
        message: 'Your booking status has been refreshed',
        type: 'booking_update',
        read: false,
        createdAt: new Date().toISOString(),
      });
    }, 1000);
  }, [addNotification]);

  const handleCallProvider = async () => {
    try {
      const phoneUrl = `tel:${currentBooking.provider.phone}`;
      const canOpen = await Linking.canOpenURL(phoneUrl);
      if (canOpen) {
        await Linking.openURL(phoneUrl);
      } else {
        Alert.alert('Error', 'Unable to make phone call');
      }
    } catch {
      Alert.alert('Error', 'Unable to make phone call');
    }
  };

  const handleMessageProvider = () => {
    router.push('/customer/chat');
  };

  const handleCancelBooking = () => {
    Alert.alert(
      'Cancel Booking',
      'Are you sure you want to cancel this booking? This action cannot be undone.',
      [
        { text: 'Keep Booking', style: 'cancel' },
        {
          text: 'Cancel Booking',
          style: 'destructive',
          onPress: () => {
            addNotification({
              id: Date.now().toString(),
              title: 'Booking Cancelled',
              message: 'Your booking has been cancelled successfully.',
              type: 'booking_update',
              read: false,
              createdAt: new Date().toISOString(),
            });
            router.back();
          },
        },
      ]
    );
  };

  const handleRateService = () => {
    Alert.alert('Rate Service', 'Thank you for using our service! Rating feature coming soon.');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return colors.success;
      case 'IN_PROGRESS':
        return colors.primary;
      case 'CONFIRMED':
        return colors.info;
      case 'ON_WAY':
        return colors.warning;
      default:
        return colors.gray[400];
    }
  };

  const getTimelineIcon = (status: string, completed: boolean) => {
    if (!completed) return 'ellipse-outline';

    switch (status) {
      case 'CONFIRMED':
        return 'checkmark-circle';
      case 'ASSIGNED':
        return 'person-circle';
      case 'ON_WAY':
        return 'car';
      case 'IN_PROGRESS':
        return 'build';
      case 'COMPLETED':
        return 'checkmark-done-circle';
      default:
        return 'checkmark-circle';
    }
  };

  return (
    <SafeAreaView style={bookingTrackingScreenStyles.container}>
      <TopNavbar
        title="Track Booking"
        showBack={true}
        onBackPress={() => router.back()}
        iconColor={colors.text.primary}
      />

      <ScrollView
        style={bookingTrackingScreenStyles.content}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Service Overview Card */}
        <View style={bookingTrackingScreenStyles.bookingCard}>
          <View style={bookingTrackingScreenStyles.serviceRow}>
            <View style={{ flex: 1 }}>
              <Text style={bookingTrackingScreenStyles.serviceName}>{currentBooking.service}</Text>
              <Text style={bookingTrackingScreenStyles.bookingDate}>
                {currentBooking.date} • {currentBooking.time}
              </Text>
              <Text style={bookingTrackingScreenStyles.servicePrice}>${currentBooking.amount}</Text>
            </View>
            <View style={[
              bookingTrackingScreenStyles.statusBadge,
              { backgroundColor: getStatusColor(currentBooking.status) }
            ]}>
              <Ionicons
                name="build"
                size={16}
                color={colors.white}
                style={{ marginRight: 4 }}
              />
              <Text style={[bookingTrackingScreenStyles.statusText, { color: colors.white }]}>
                In Progress
              </Text>
            </View>
          </View>
        </View>

        {/* Progress Card */}
        <View style={bookingTrackingScreenStyles.statusCard}>
          <Text style={bookingTrackingScreenStyles.statusTitle}>Service Progress</Text>

          <View style={bookingTrackingScreenStyles.progressContainer}>
            <View style={bookingTrackingScreenStyles.progressBar}>
              <View
                style={[
                  bookingTrackingScreenStyles.progressFill,
                  { width: `${currentBooking.progress}%` }
                ]}
              />
            </View>
            <Text style={bookingTrackingScreenStyles.progressText}>
              {currentBooking.progress}% Complete
            </Text>
          </View>

          <Text style={bookingTrackingScreenStyles.estimatedTime}>
            Estimated completion: {currentBooking.estimatedCompletion}
          </Text>
        </View>

        {/* Timeline Card */}
        <View style={bookingTrackingScreenStyles.statusCard}>
          <Text style={bookingTrackingScreenStyles.statusTitle}>Service Timeline</Text>

          {currentBooking.timeline.map((step) => (
            <View key={step.status} style={bookingTrackingScreenStyles.statusContainer}>
              <View style={[
                bookingTrackingScreenStyles.statusIcon,
                {
                  backgroundColor: step.completed ? getStatusColor(step.status) : colors.gray[200]
                }
              ]}>
                <Ionicons
                  name={getTimelineIcon(step.status, step.completed)}
                  size={20}
                  color={step.completed ? colors.white : colors.gray[400]}
                />
              </View>
              <View style={bookingTrackingScreenStyles.statusInfo}>
                <Text style={[
                  bookingTrackingScreenStyles.statusText,
                  { color: step.completed ? colors.text.primary : colors.text.tertiary }
                ]}>
                  {step.label}
                </Text>
                {step.time && (
                  <Text style={bookingTrackingScreenStyles.statusTime}>{step.time}</Text>
                )}
              </View>
            </View>
          ))}
        </View>

        {/* Provider Card */}
        <View style={bookingTrackingScreenStyles.providerCard}>
          <Text style={bookingTrackingScreenStyles.providerTitle}>Your Provider</Text>

          <View style={bookingTrackingScreenStyles.providerInfo}>
            <View style={bookingTrackingScreenStyles.providerAvatar}>
              <Text style={bookingTrackingScreenStyles.providerAvatarText}>
                {currentBooking.provider.avatar}
              </Text>
            </View>
            <View style={bookingTrackingScreenStyles.providerDetails}>
              <Text style={bookingTrackingScreenStyles.providerName}>
                {currentBooking.provider.name}
              </Text>
              <Text style={bookingTrackingScreenStyles.providerRating}>
                ⭐ {currentBooking.provider.rating} rating
              </Text>
            </View>
          </View>

          <View style={bookingTrackingScreenStyles.providerActions}>
            <TouchableOpacity
              style={[bookingTrackingScreenStyles.actionButton, bookingTrackingScreenStyles.callButton]}
              onPress={handleCallProvider}
            >
              <Ionicons name="call" size={18} color={colors.white} />
              <Text style={bookingTrackingScreenStyles.actionButtonText}>Call</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[bookingTrackingScreenStyles.actionButton, bookingTrackingScreenStyles.messageButton]}
              onPress={handleMessageProvider}
            >
              <Ionicons name="chatbubble" size={18} color={colors.white} />
              <Text style={bookingTrackingScreenStyles.actionButtonText}>Message</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Updates Card */}
        <View style={bookingTrackingScreenStyles.statusCard}>
          <Text style={bookingTrackingScreenStyles.statusTitle}>Recent Updates</Text>

          {currentBooking.recentUpdates.map((update) => (
            <View key={update.id} style={bookingTrackingScreenStyles.updateItem}>
              <View style={bookingTrackingScreenStyles.updateIcon}>
                <Ionicons
                  name={update.type === 'progress' ? 'checkmark-circle' : 'information-circle'}
                  size={16}
                  color={colors.primary}
                />
              </View>
              <View style={bookingTrackingScreenStyles.updateContent}>
                <Text style={bookingTrackingScreenStyles.updateMessage}>{update.message}</Text>
                <Text style={bookingTrackingScreenStyles.updateTimestamp}>{update.timestamp}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Service Details Card */}
        <View style={bookingTrackingScreenStyles.summaryCard}>
          <Text style={bookingTrackingScreenStyles.summaryTitle}>Service Details</Text>

          <View style={bookingTrackingScreenStyles.summaryRow}>
            <Text style={bookingTrackingScreenStyles.summaryLabel}>Service Address</Text>
          </View>
          <Text style={bookingTrackingScreenStyles.addressText}>{currentBooking.address}</Text>

          {currentBooking.specialInstructions && (
            <>
              <View style={bookingTrackingScreenStyles.summaryDivider} />
              <View style={bookingTrackingScreenStyles.summaryRow}>
                <Text style={bookingTrackingScreenStyles.summaryLabel}>Special Instructions</Text>
              </View>
              <Text style={bookingTrackingScreenStyles.instructionsText}>
                {currentBooking.specialInstructions}
              </Text>
            </>
          )}

          <View style={bookingTrackingScreenStyles.summaryDivider} />
          <View style={bookingTrackingScreenStyles.summaryRow}>
            <Text style={bookingTrackingScreenStyles.totalLabel}>Total Amount</Text>
            <Text style={bookingTrackingScreenStyles.totalValue}>${currentBooking.amount}</Text>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={bookingTrackingScreenStyles.footer}>
          <View style={bookingTrackingScreenStyles.footerButtons}>
            <Button
              title="Cancel Booking"
              onPress={handleCancelBooking}
              style={bookingTrackingScreenStyles.cancelButton}
            />
            {currentBooking.status === 'COMPLETED' && (
              <Button
                title="Rate Service"
                onPress={handleRateService}
                style={bookingTrackingScreenStyles.rateButton}
              />
            )}
          </View>
        </View>
      </ScrollView>

      <CustomerNavigation />
    </SafeAreaView>
  );
};
