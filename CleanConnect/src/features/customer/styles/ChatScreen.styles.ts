import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const chatScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    marginRight: spacing.md,
  },
  backButtonText: {
    fontSize: 24,
  },
  headerInfo: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.neutral,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
  },
  actionButtonText: {
    fontSize: 18,
  },
  messagesContainer: {
    flex: 1,
    paddingHorizontal: spacing.lg,
  },
  messagesList: {
    paddingVertical: spacing.md,
  },
  messageContainer: {
    marginBottom: spacing.md,
    maxWidth: '80%',
  },
  myMessage: {
    alignSelf: 'flex-end',
  },
  theirMessage: {
    alignSelf: 'flex-start',
  },
  messageBubble: {
    borderRadius: 16,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  myMessageBubble: {
    backgroundColor: colors.primary,
  },
  theirMessageBubble: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  messageText: {
    fontSize: typography.sizes.md,
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily.regular,
  },
  myMessageText: {
    color: colors.white,
  },
  theirMessageText: {
    color: colors.gray[900],
  },
  messageTime: {
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  myMessageTime: {
    color: colors.white,
    opacity: 0.8,
    textAlign: 'right',
  },
  theirMessageTime: {
    color: colors.gray[500],
    textAlign: 'left',
  },
  messageStatus: {
    fontSize: typography.sizes.xs,
    marginTop: spacing.xs,
    textAlign: 'right',
    color: colors.white,
    opacity: 0.8,
    fontFamily: typography.fontFamily.regular,
  },
  typingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  typingText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontStyle: 'italic',
    fontFamily: typography.fontFamily.regular,
  },
  inputContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  textInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: colors.gray[300],
    borderRadius: 20,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    fontSize: typography.sizes.md,
    maxHeight: 100,
    fontFamily: typography.fontFamily.regular,
  },
  attachButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[200],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  attachButtonText: {
    fontSize: 18,
  },
  sendButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
  },
  sendButtonDisabled: {
    backgroundColor: colors.gray[300],
  },
  sendButtonText: {
    fontSize: 18,
  },
  // Additional styles for the component
  customerMessage: {
    alignSelf: 'flex-end',
  },
  providerMessage: {
    alignSelf: 'flex-start',
  },
  customerBubble: {
    backgroundColor: colors.primary,
  },
  providerBubble: {
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
  },
  customerText: {
    color: colors.white,
  },
  providerText: {
    color: colors.gray[900],
  },
  messageMeta: {
    marginTop: spacing.xs,
  },
  providerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  providerAvatar: {
    fontSize: 40,
    marginRight: spacing.md,
  },
  providerDetails: {
    flex: 1,
  },
  providerName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  providerService: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: spacing.xs,
  },
  statusText: {
    fontSize: typography.sizes.xs,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  chatContainer: {
    flex: 1,
  },
  typingContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
  },
  typingBubble: {
    backgroundColor: colors.white,
    borderRadius: 16,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    alignSelf: 'flex-start',
    maxWidth: '80%',
    flexDirection: 'row',
    alignItems: 'center',
  },
  typingIndicatorText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontStyle: 'italic',
    marginRight: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  typingDots: {
    flexDirection: 'row',
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: colors.gray[400],
    marginHorizontal: 1,
  },
  dot1: {
    // Animation delay would be handled in component
  },
  dot2: {
    // Animation delay would be handled in component
  },
  dot3: {
    // Animation delay would be handled in component
  },
});
