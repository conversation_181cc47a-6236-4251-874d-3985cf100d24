import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const bookingDetailScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
    padding: spacing.lg,
  },
  header: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamilyBold,
    color: colors.primary,
    marginBottom: spacing.lg,
  },
  card: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  type: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamilySemiBold,
    color: colors.gray[900],
  },
  status: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamilySemiBold,
  },
  date: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily,
    marginBottom: spacing.xs,
  },
  price: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontFamily: typography.fontFamilyBold,
    marginBottom: spacing.md,
  },
  address: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontFamily: typography.fontFamily,
    marginBottom: spacing.xs,
  },
  details: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily,
    marginBottom: spacing.md,
  },
  summaryCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 1,
  },
  summaryTitle: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamilySemiBold,
    color: colors.gray[900],
    marginBottom: spacing.sm,
  },
  summaryText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontFamily: typography.fontFamily,
    marginBottom: spacing.xs,
  },
  button: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.md,
    alignItems: 'center',
    marginBottom: spacing.xl,
  },
  buttonText: {
    color: colors.white,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamilySemiBold,
  },
});

export type BookingStatus = 'Confirmed' | 'Pending' | 'Completed';

export const statusColors: Record<BookingStatus, string> = {
  Confirmed: colors.success,
  Pending: colors.warning,
  Completed: colors.primary,
};
