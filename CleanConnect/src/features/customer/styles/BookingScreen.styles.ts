import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography, textStyles } from '@/constants/typography';

export const bookingScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  headerContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  header: {
    fontSize: typography.sizes.xxl,
    fontFamily: typography.fontFamily.bold,
    color: colors.text.primary,
    lineHeight: typography.sizes.xxl * 1.25,
  },
  helpButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
  },
  helpButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary,
    lineHeight: typography.sizes.md * 1.5,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
  },

  // Empty State Styles
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xxxxl,
  },
  emptyStateContent: {
    alignItems: 'center',
    maxWidth: 300,
  },
  emptyStateTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.semibold,
    color: colors.text.primary,
    textAlign: 'center',
    marginBottom: spacing.md,
    lineHeight: typography.sizes.xl * 1.25,
  },
  emptyStateDescription: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.secondary,
    textAlign: 'center',
    marginBottom: spacing.xxxl,
    lineHeight: typography.sizes.md * 1.5,
  },
  exploreButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    backgroundColor: 'transparent',
  },
  exploreButtonText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.medium,
    color: colors.primary,
    marginRight: spacing.sm,
    lineHeight: typography.sizes.md * 1.5,
  },

  // Help Screen Styles
  helpContent: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.xl,
  },
  helpSectionTitle: {
    fontSize: typography.sizes.xl,
    fontFamily: typography.fontFamily.bold,
    color: colors.text.primary,
    marginBottom: spacing.xl,
    lineHeight: typography.sizes.xl * 1.25,
  },
  helpTopicsList: {
    backgroundColor: colors.white,
    borderRadius: 12,
    overflow: 'hidden',
  },
  helpTopic: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border.light,
  },
  helpTopicContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  helpTopicText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.regular,
    color: colors.text.primary,
    marginLeft: spacing.md,
    lineHeight: typography.sizes.md * 1.5,
  },

  // Existing Booking Card Styles (for when bookings exist)
  card: {
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    marginBottom: spacing.lg,
    marginTop: spacing.lg,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  type: {
    fontSize: typography.sizes.lg,
    fontFamily: typography.fontFamily.semibold,
    color: colors.text.primary,
    lineHeight: typography.sizes.lg * 1.25,
  },
  status: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    lineHeight: typography.sizes.md * 1.25,
  },
  date: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
    marginBottom: spacing.xs,
    lineHeight: typography.sizes.sm * 1.5,
  },
  price: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
    lineHeight: typography.sizes.md * 1.25,
  },
  button: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.md,
    alignItems: 'center',
  },
  buttonText: {
    color: colors.white,
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily.semibold,
    lineHeight: typography.sizes.md * 1.25,
  },
});

export type BookingStatus = 'Confirmed' | 'Pending' | 'Completed';

export const statusColors: Record<BookingStatus, string> = {
  Confirmed: colors.success,
  Pending: colors.warning,
  Completed: colors.primary,
};
