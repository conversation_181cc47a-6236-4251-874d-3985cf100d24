import { StyleSheet, ViewStyle, TextStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const notificationCenterScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  } as ViewStyle,
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  } as ViewStyle,

  // Count container for TopNavbar layout
  countContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  } as ViewStyle,
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,
  markAllButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
  } as ViewStyle,
  markAllText: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,
  content: {
    flex: 1,
  } as ViewStyle,
  filterContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  } as ViewStyle,
  filterScrollView: {
    flexDirection: 'row',
  } as ViewStyle,
  filterButton: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 20,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
  } as ViewStyle,
  filterButtonActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  } as ViewStyle,
  filterText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontFamily: typography.fontFamily,
  } as TextStyle,
  filterTextActive: {
    color: colors.white,
    fontWeight: typography.weights.medium,
  } as TextStyle,
  notificationsList: {
    paddingHorizontal: spacing.lg,
  } as ViewStyle,
  notificationCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginVertical: spacing.sm,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  } as ViewStyle,
  notificationCardUnread: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  } as ViewStyle,
  notificationContent: {
    padding: spacing.lg,
  } as ViewStyle,
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.sm,
  } as ViewStyle,
  notificationInfo: {
    flex: 1,
    marginRight: spacing.md,
  } as ViewStyle,
  notificationTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamilySemiBold,
  } as TextStyle,
  notificationMessage: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily,
  } as TextStyle,
  notificationMeta: {
    alignItems: 'flex-end',
  } as ViewStyle,
  notificationTime: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily,
  } as TextStyle,
  notificationBadge: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  } as ViewStyle,
  notificationActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingTop: spacing.md,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  } as ViewStyle,
  actionButton: {
    flex: 1,
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 8,
    alignItems: 'center',
    marginHorizontal: spacing.xs,
  } as ViewStyle,
  primaryActionButton: {
    backgroundColor: colors.primary,
  } as ViewStyle,
  secondaryActionButton: {
    backgroundColor: colors.gray[200],
  } as ViewStyle,
  actionButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily,
  } as TextStyle,
  primaryActionText: {
    color: colors.white,
  } as TextStyle,
  secondaryActionText: {
    color: colors.gray[700],
  } as TextStyle,
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: spacing.xl,
  } as ViewStyle,
  emptyIcon: {
    fontSize: 64,
    marginBottom: spacing.lg,
  } as TextStyle,
  emptyTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.sm,
    textAlign: 'center',
    fontFamily: typography.fontFamilySemiBold,
  } as TextStyle,
  emptyMessage: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily,
  } as TextStyle,
  bottomSpacing: {
    height: spacing.xl,
  } as ViewStyle,
  // Additional styles for the component
  headerSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily,
  } as TextStyle,
  filterTab: {
    paddingVertical: spacing.sm,
    paddingHorizontal: spacing.md,
    borderRadius: 20,
    marginRight: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
  } as ViewStyle,
  filterTabActive: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  } as ViewStyle,
  filterTabText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    fontFamily: typography.fontFamily,
  } as TextStyle,
  filterTabTextActive: {
    color: colors.white,
    fontWeight: typography.weights.medium,
  } as TextStyle,
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    backgroundColor: colors.white,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  } as ViewStyle,
  clearButton: {
    backgroundColor: colors.warning,
  } as ViewStyle,
  emptyStateIcon: {
    fontSize: 64,
    marginBottom: spacing.lg,
  } as TextStyle,
  emptyStateTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.sm,
    textAlign: 'center',
    fontFamily: typography.fontFamilySemiBold,
  } as TextStyle,
  emptyStateDescription: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily,
  } as TextStyle,
  unreadNotification: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  } as ViewStyle,
  notificationIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  } as ViewStyle,
  notificationIconText: {
    fontSize: 20,
  } as TextStyle,
  unreadIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  } as ViewStyle,
});
