import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const addressSelectionScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.bold,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: colors.white,
    margin: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.md,
    fontFamily: typography.fontFamily.semibold,
  },
  addressCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
    marginBottom: spacing.md,
    borderWidth: 2,
    borderColor: colors.gray[200],
  },
  addressCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.white,
  },
  addressHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  addressLabelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  addressLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginRight: spacing.sm,
    fontFamily: typography.fontFamily.semibold,
  },
  primaryBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  primaryText: {
    color: colors.white,
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  },
  addressIcon: {
    fontSize: 20,
  },
  addressText: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  addAddressCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
    borderWidth: 2,
    borderColor: colors.gray[200],
    borderStyle: 'dashed',
    alignItems: 'center',
  },
  addAddressCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.white,
  },
  addAddressIcon: {
    fontSize: 24,
    marginBottom: spacing.sm,
  },
  addAddressText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.medium,
  },
  form: {
    marginTop: spacing.md,
  },
  saveButton: {
    marginTop: spacing.lg,
  },
  summaryCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  summaryValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.medium,
  },
  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  continueButton: {
    width: '100%',
  },
});
