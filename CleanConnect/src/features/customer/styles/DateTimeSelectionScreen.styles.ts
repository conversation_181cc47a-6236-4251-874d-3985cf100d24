import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const dateTimeSelectionScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamilyBold,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: colors.white,
    margin: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.md,
    fontFamily: typography.fontFamilySemiBold,
  },
  calendarContainer: {
    marginBottom: spacing.lg,
  },
  calendar: {
    borderRadius: 8,
    backgroundColor: colors.white,
  },
  dateGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dateButton: {
    width: '13%',
    aspectRatio: 1,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.xs,
  },
  dateButtonAvailable: {
    backgroundColor: colors.neutral,
  },
  dateButtonSelected: {
    backgroundColor: colors.primary,
  },
  dateButtonUnavailable: {
    backgroundColor: colors.gray[100],
  },
  dateText: {
    fontSize: typography.sizes.sm,
    fontFamily: typography.fontFamily,
  },
  dateTextAvailable: {
    color: colors.gray[900],
  },
  dateTextSelected: {
    color: colors.white,
    fontWeight: typography.weights.semibold,
  },
  dateTextUnavailable: {
    color: colors.gray[400],
  },
  timeSlotContainer: {
    marginBottom: spacing.lg,
  },
  timeSlotGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlotButton: {
    width: '48%',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: spacing.sm,
    borderWidth: 1,
  },
  timeSlotAvailable: {
    backgroundColor: colors.white,
    borderColor: colors.gray[300],
  },
  timeSlotSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  timeSlotUnavailable: {
    backgroundColor: colors.gray[100],
    borderColor: colors.gray[200],
  },
  timeSlotText: {
    fontSize: typography.sizes.md,
    fontFamily: typography.fontFamily,
  },
  timeSlotTextAvailable: {
    color: colors.gray[900],
  },
  timeSlotTextSelected: {
    color: colors.white,
    fontWeight: typography.weights.semibold,
  },
  timeSlotTextUnavailable: {
    color: colors.gray[400],
  },
  timeSlotPrice: {
    fontSize: typography.sizes.sm,
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily,
  },
  timeSlotPriceAvailable: {
    color: colors.primary,
  },
  timeSlotPriceSelected: {
    color: colors.white,
  },
  timeSlotPriceUnavailable: {
    color: colors.gray[400],
  },
  summaryCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  summaryLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily,
  },
  summaryValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    fontFamily: typography.fontFamily,
  },
  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  continueButton: {
    width: '100%',
  },
  // Additional styles for the component
  datesContainer: {
    flexDirection: 'row',
    paddingHorizontal: spacing.sm,
  },
  dateCard: {
    alignItems: 'center',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    marginHorizontal: spacing.xs,
    borderRadius: 8,
    backgroundColor: colors.white,
    borderWidth: 1,
    borderColor: colors.gray[200],
    minWidth: 60,
  },
  dateCardSelected: {
    backgroundColor: colors.primary,
    borderColor: colors.primary,
  },
  dateDay: {
    fontSize: typography.sizes.xs,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily,
  },
  dateDaySelected: {
    color: colors.white,
  },
  dateNumber: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamilySemiBold,
  },
  dateNumberSelected: {
    color: colors.white,
  },
  selectedDateText: {
    fontSize: typography.sizes.md,
    color: colors.gray[700],
    marginBottom: spacing.md,
    fontFamily: typography.fontFamily,
  },
  timeSlotsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlot: {
    width: '48%',
    paddingVertical: spacing.md,
    paddingHorizontal: spacing.sm,
    borderRadius: 8,
    alignItems: 'center',
    marginBottom: spacing.sm,
    borderWidth: 1,
    borderColor: colors.gray[300],
    backgroundColor: colors.white,
  },
  unavailableText: {
    fontSize: typography.sizes.xs,
    color: colors.gray[400],
    marginTop: spacing.xs,
    fontFamily: typography.fontFamily,
  },
});
