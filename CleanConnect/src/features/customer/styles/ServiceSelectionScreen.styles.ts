import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

const { width } = Dimensions.get('window');

export const serviceSelectionScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    lineHeight: typography.lineHeights.normal,
    textAlign: 'center',
    marginBottom: spacing.sm,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal,
  },
  // Promotional banner section
  bannerContainer: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
  },
  banner: {
    backgroundColor: colors.primary,
    borderRadius: 20,
    padding: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 120,
    overflow: 'hidden',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
  },
  bannerContent: {
    flex: 1,
    paddingRight: spacing.lg,
  },
  bannerTitle: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.xs,
    // lineHeight: typography.lineHeights.tight,
  },
  bannerSubtitle: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontFamily: typography.fontFamily.regular,
    opacity: 0.9,
    marginBottom: spacing.md,
    // lineHeight: typography.lineHeights.normal,
  },
  bannerButton: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  bannerButtonText: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.primary,
    fontFamily: typography.fontFamily.semibold,
  },
  bannerIllustration: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.white + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  bannerIcon: {
    fontSize: 40,
  },
  content: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  servicesContainer: {
    padding: spacing.md,
  },
  servicesSection: {
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.lg,
    lineHeight: typography.lineHeights.normal,
  },
  serviceCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: colors.gray[100],
    overflow: 'hidden',
  },
  serviceCardSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.white,
    shadowColor: colors.primary,
    shadowOpacity: 0.15,
    transform: [{ scale: 0.98 }],
  },
  serviceContent: {
    padding: spacing.md,
  },
  serviceHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.md,
  },
  serviceInfo: {
    flex: 1,
    marginRight: spacing.md,
  },
  serviceIcon: {
    fontSize: 32,
    marginBottom: spacing.sm,
  },
  serviceName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  serviceDescription: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    // lineHeight: typography.lineHeights.normal,
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  serviceDuration: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    fontFamily: typography.fontFamily.regular,
  },
  servicePricing: {
    alignItems: 'flex-end',
  },
  servicePrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.bold,
  },
  servicePriceUnit: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    fontFamily: typography.fontFamily.regular,
  },
  serviceFeatures: {
    marginTop: spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.xs,
  },
  featureIcon: {
    fontSize: 16,
    marginRight: spacing.sm,
    color: colors.success,
  },
  featureText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[700],
    flex: 1,
    fontFamily: typography.fontFamily.regular,
  },
  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  selectedServiceContainer: {
    marginBottom: spacing.lg,
  },
  selectedServiceLabel: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily.regular,
  },
  selectedServiceCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  selectedServiceName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
  },
  selectedServicePrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  },
  continueButton: {
    width: '100%',
  },
  // Additional styles for the component
  serviceIconContainer: {
    marginRight: spacing.md,
  },
  serviceTitleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.xs,
  },
  popularBadge: {
    backgroundColor: colors.warning,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
  },
  popularText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.semibold,
    fontFamily: typography.fontFamily.semibold,
  },
  serviceMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
  },
  selectedServiceInfo: {
    marginBottom: spacing.lg,
  },
  selectedServiceTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  // New styles for modern grid layout
  scrollContent: {
    paddingBottom: spacing.xxl,
  },
  servicesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    gap: spacing.lg,
  },
  // Service card variants for different categories
  serviceCardCompact: {
    width: (width - spacing.lg * 3) / 2, // 2 columns with proper spacing
    aspectRatio: 1,
    backgroundColor: colors.white,
    borderRadius: 20,
    padding: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
    borderWidth: 1,
    borderColor: colors.gray[100],
    marginBottom: spacing.md,
  },
  serviceCardCompactSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.primary + '05',
    shadowColor: colors.primary,
    shadowOpacity: 0.2,
  },
  serviceIconWrapper: {
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  serviceIconWrapperSelected: {
    backgroundColor: colors.primary + '15',
  },
  serviceIconCompact: {
    fontSize: 28,
  },
  serviceNameCompact: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal,
  },
 
  // Modern service card styles matching the design
  serviceCardModern: {
    width: (width - spacing.lg * 3) / 2, // 2 columns with proper spacing
    backgroundColor: colors.white,
    borderRadius: 24,
    padding: spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 6,
    borderWidth: 0,
    marginBottom: spacing.xl,
    minHeight: 160,
  },
  serviceCardModernSelected: {
    backgroundColor: colors.primary + '08',
    shadowColor: colors.primary,
    shadowOpacity: 0.25,
    transform: [{ scale: 1.02 }],
    borderWidth: 2,
    borderColor: colors.primary,
  },
  serviceImageContainer: {
    width: 90,
    height: 90,
    borderRadius: 24,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  },
  serviceImageContainerSelected: {
    backgroundColor: colors.primary + '15',
  },
  serviceImage: {
    fontSize: 48,
  },
  serviceNameModern: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    lineHeight: typography.lineHeights.normal,
    marginTop: spacing.xs,
  },
  // Header styles for inspired design
  headerModern: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.md,
    paddingBottom: spacing.md,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[100],
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.sm,
  },
  logoIcon: {
    fontSize: 24,
    marginRight: spacing.sm,
  },
  headerTitleModern: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.bold,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerActionButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: spacing.sm,
    position: 'relative',
  },
  headerActionIcon: {
    fontSize: 18,
  },
  notificationBadgeHeader: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: colors.error,
    borderRadius: 8,
    width: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  notificationCountHeader: {
    color: colors.white,
    fontSize: 10,
    fontWeight: typography.weights.bold,
    fontFamily: typography.fontFamily.bold,
  },
  // Modern banner styles with gradient-like appearance
  bannerModern: {
    backgroundColor: '#FF6B6B', // Coral red background
    borderRadius: 20,
    padding: spacing.xl,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 140,
    overflow: 'hidden',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 8,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    position: 'relative',
  },
  bannerContentModern: {
    flex: 1,
    paddingRight: spacing.lg,
  },
  bannerTitleModern: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.xs,
    lineHeight: typography.lineHeights.tight,
  },
  bannerSubtitleModern: {
    fontSize: typography.sizes.sm,
    color: colors.white,
    fontFamily: typography.fontFamily.regular,
    opacity: 0.9,
    marginBottom: spacing.md,
    lineHeight: typography.lineHeights.normal,
  },
  bannerButtonModern: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 20,
    alignSelf: 'flex-start',
  },
  bannerButtonTextModern: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.primary,
    fontFamily: typography.fontFamily.semibold,
  },
  bannerIllustrationModern: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: '#4ECDC4', // Mint green background for illustration
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  bannerIconModern: {
    fontSize: 60,
  },
  // Additional decorative elements for the banner
  bannerDecorative: {
    position: 'absolute',
    top: 10,
    right: 20,
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: '#FFD93D',
  },
  bannerDecorative2: {
    position: 'absolute',
    bottom: 15,
    left: 30,
    width: 15,
    height: 15,
    borderRadius: 7.5,
    backgroundColor: '#6BCF7F',
  },
  // Inspired design styles matching the reference image - 3 columns
  serviceCardInspired: {
    width: (width - spacing.lg * 4) / 3, // Perfect 3-column grid
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 6,
    elevation: 2,
    marginBottom: spacing.md,
    minHeight: 110,
  },
  serviceCardInspiredSelected: {
    backgroundColor: colors.primary + '08',
    shadowColor: colors.primary,
    shadowOpacity: 0.2,
    transform: [{ scale: 1.02 }],
    borderWidth: 2,
    borderColor: colors.primary,
  },
  serviceImageContainerInspired: {
    width: 90,
    height: 70,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  serviceImageContainerInspiredSelected: {
    transform: [{ scale: 1.1 }],
  },
  serviceImageInspired: {
    fontSize: 28,
  },
  serviceNameInspired: {
    fontSize: typography.sizes.xs,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    lineHeight: typography.sizes.xs * 1.4,
  },



  // Perfect 3-column grid layout
  servicesGridInspired: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    // paddingHorizontal: spacing.m,
    // paddingTop: spacing.md,
    gap: spacing.xs,
  },
  // Inspired banner styles matching the reference image
  bannerInspired: {
    backgroundColor: '#FF6B6B', // Coral red background
    borderRadius: 20,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    minHeight: 140,
    overflow: 'hidden',
    shadowColor: '#FF6B6B',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 12,
    elevation: 6,
    marginHorizontal: spacing.lg,
    marginBottom: spacing.lg,
    position: 'relative',
  },
  bannerContentInspired: {
    flex: 1,
    paddingRight: spacing.lg,
  },
  bannerTitleInspired: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.white,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.md,
    lineHeight: typography.sizes.xl * 1.2,
  },
  bannerButtonInspired: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  bannerButtonTextInspired: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: '#FF6B6B',
    fontFamily: typography.fontFamily.semibold,
  },
  bannerIllustrationInspired: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#4ECDC4', // Mint green background for illustration
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  bannerIconInspired: {
    fontSize: 50,
  },

  // Clean design styles matching the reference image - 2 columns
  serviceCardClean: {
    width: (width - spacing.lg * 3) / 2, // 2-column grid
    backgroundColor: colors.white,
    borderRadius: 16,
    padding: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: spacing.lg,
    minHeight: 140,
    borderWidth: 2,
    borderColor: colors.gray[100],
  },
  serviceCardCleanSelected: {
    borderColor: colors.primary,
    backgroundColor: colors.white,
    shadowColor: colors.primary,
    shadowOpacity: 0.15,
    transform: [{ scale: 1.02 }],
  },
  serviceImageContainerClean: {
    width: 80,
    height: 80,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.md,
  },
  serviceImageContainerCleanSelected: {
    transform: [{ scale: 1.05 }],
  },
  serviceImageClean: {
    fontSize: 32,
  },
  serviceNameClean: {
    fontSize: typography.sizes.sm,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    lineHeight: typography.sizes.sm * typography.lineHeights.normal,
  },
  // Clean grid layout
  servicesGridClean: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    paddingHorizontal: spacing.lg,
    gap: spacing.md,
  },
  // Header styles for clean design
  headerClean: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingTop: spacing.xl,
    paddingBottom: spacing.lg,
    alignItems: 'center',
  },
  headerTitleClean: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
    textAlign: 'center',
    lineHeight: typography.sizes.xxl * typography.lineHeights.tight,
    marginBottom: spacing.sm,
  },
  // Next button styles
  nextButtonContainer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    paddingBottom: spacing.xl,
  },
  nextButton: {
    backgroundColor: colors.primary,
    borderRadius: 12,
    paddingVertical: spacing.lg,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  nextButtonText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  },
  nextButtonDisabled: {
    backgroundColor: colors.gray[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  nextButtonTextDisabled: {
    color: colors.gray[500],
  },
});
