import { StyleSheet, Dimensions } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography, textStyles } from '@/constants/typography';

const { width } = Dimensions.get('window');

export const homeScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background.secondary,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
    paddingBottom: spacing.xl,
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: spacing.lg,
  },
  logoContainer: {
    flex: 1,
  },
  logoWrapper: {
    marginBottom: spacing.xs,
  },
  logoText: {
    ...textStyles.h4,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  },
  locationContainer: {
    marginTop: spacing.xs / 2,
  },
  locationLabel: {
    ...textStyles.bodySmall,
    color: colors.text.secondary,
    marginBottom: spacing.xs / 2,
  },
  locationText: {
    ...textStyles.body,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.medium,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.sm,
  },
  actionButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.background.tertiary,
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  actionIcon: {
    fontSize: 20,
  },
  notificationBadge: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: colors.error,
    borderRadius: 8,
    minWidth: 16,
    height: 16,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: colors.white,
  },
  notificationCount: {
    color: colors.white,
    fontSize: typography.sizes.xs - 1,
    fontWeight: typography.weights.bold,
    fontFamily: typography.fontFamily.bold,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.background.tertiary,
    borderRadius: 12,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.md,
    marginBottom: spacing.lg,
  },
  searchIcon: {
    fontSize: 18,
    marginRight: spacing.sm,
  },
  searchPlaceholder: {
    ...textStyles.body,
    color: colors.text.tertiary,
    flex: 1,
  },
  promoBanner: {
    backgroundColor: colors.primary,
    borderRadius: 16,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  promoContent: {
    flex: 1,
  },
  promoTitle: {
    ...textStyles.h5,
    color: colors.white,
    marginBottom: spacing.xs / 2,
  },
  promoSubtitle: {
    ...textStyles.bodySmall,
    color: colors.white,
    opacity: 0.9,
    marginBottom: spacing.xs / 2,
  },
  promoDescription: {
    ...textStyles.bodySmall,
    color: colors.white,
    opacity: 0.8,
  },
  promoIcon: {
    marginLeft: spacing.md,
  },
  promoEmoji: {
    fontSize: 32,
  },
  section: {
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
  },
  sectionTitle: {
    ...textStyles.h5,
    color: colors.text.primary,
    marginBottom: spacing.lg,
  },
  categoriesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  categoryCard: {
    width: (width - spacing.xl * 2 - spacing.md * 2) / 3,
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  categoryIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  categoryIcon: {
    fontSize: 24,
  },
  categoryName: {
    ...textStyles.bodySmall,
    color: colors.text.primary,
    textAlign: 'center',
  },
  dotsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.gray[300],
    marginHorizontal: spacing.xs / 2,
  },
  activeDot: {
    backgroundColor: colors.primary,
  },
  membershipBanner: {
    backgroundColor: colors.accent,
    borderRadius: 16,
    padding: spacing.lg,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
  membershipContent: {
    flex: 1,
  },
  membershipTitle: {
    ...textStyles.h5,
    color: colors.white,
    marginBottom: spacing.sm,
  },
  membershipSubtitle: {
    ...textStyles.bodySmall,
    color: colors.white,
    opacity: 0.9,
    lineHeight: typography.sizes.sm * 1.3,
  },
  buyNowButton: {
    backgroundColor: colors.warning,
    borderRadius: 8,
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
    marginTop: spacing.sm,
    alignSelf: 'flex-start',
  },
  buyNowText: {
    ...textStyles.buttonSmall,
    color: colors.white,
  },
  membershipIcon: {
    marginLeft: spacing.md,
  },
  crownEmoji: {
    fontSize: 40,
  },
  horizontalScroll: {
    marginHorizontal: -spacing.xl,
    paddingHorizontal: spacing.xl,
  },
  packageCard: {
    width: 160,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginRight: spacing.md,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
  },
  packageImageContainer: {
    width: '100%',
    height: 80,
    backgroundColor: colors.background.tertiary,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  packageImage: {
    fontSize: 32,
  },
  packageName: {
    ...textStyles.bodySmall,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.sm * 1.3,
  },
  packagePriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  packagePrice: {
    ...textStyles.body,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.semibold,
    marginRight: spacing.xs,
  },
  packageOriginalPrice: {
    ...textStyles.bodySmall,
    color: colors.text.tertiary,
    textDecorationLine: 'line-through',
  },
  addButton: {
    backgroundColor: colors.primary,
    borderRadius: 8,
    paddingVertical: spacing.sm,
    alignItems: 'center',
  },
  addButtonText: {
    ...textStyles.buttonSmall,
    color: colors.white,
  },
  dealsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dealCard: {
    width: (width - spacing.xl * 2 - spacing.md) / 2,
    backgroundColor: colors.white,
    borderRadius: 12,
    padding: spacing.md,
    marginBottom: spacing.md,
    shadowColor: colors.shadow.light,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 1,
    shadowRadius: 4,
    elevation: 2,
  },
  dealImageContainer: {
    width: '100%',
    height: 80,
    backgroundColor: colors.background.tertiary,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.sm,
  },
  dealImage: {
    fontSize: 32,
  },
  dealName: {
    ...textStyles.bodySmall,
    color: colors.text.primary,
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.sm * 1.3,
  },
  dealPrice: {
    ...textStyles.body,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.semibold,
    marginBottom: spacing.xs,
  },
  dealOriginalPrice: {
    ...textStyles.bodySmall,
    color: colors.text.tertiary,
    textDecorationLine: 'line-through',
    marginBottom: spacing.sm,
  },
  bottomSpacing: {
    height: spacing.xxxxl,
  },
});
