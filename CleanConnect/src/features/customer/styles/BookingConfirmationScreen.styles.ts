import { StyleSheet } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

export const bookingConfirmationScreenStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[50],
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.md,
  },
  headerContent: {
    flex: 1,
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.bold,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: colors.white,
    margin: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.md,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
  },
  editLink: {
    fontSize: typography.sizes.md,
    color: colors.primary,
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.regular,
  },
  serviceCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  serviceHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  serviceIcon: {
    fontSize: 32,
    marginRight: spacing.md,
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  serviceDuration: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  servicePrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  },
  detailCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  detailIcon: {
    fontSize: 20,
    marginRight: spacing.md,
  },
  detailText: {
    fontSize: typography.sizes.md,
    color: colors.gray[900],
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.regular,
  },
  addressInfo: {
    flex: 1,
  },
  addressLabel: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  addressText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily.regular,
  },
  providerInfo: {
    flex: 1,
  },
  providerStatus: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  providerDescription: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily.regular,
  },
  pricingCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  pricingRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.sm,
  },
  pricingLabel: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  pricingValue: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.regular,
  },
  pricingDivider: {
    height: 1,
    backgroundColor: colors.gray[300],
    marginVertical: spacing.sm,
  },
  totalLabel: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
  },
  totalValue: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  },
  termsCard: {
    backgroundColor: colors.neutral,
    borderRadius: 8,
    padding: spacing.lg,
  },
  termsText: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily.regular,
  },
  footer: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderTopWidth: 1,
    borderTopColor: colors.gray[200],
  },
  totalSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: spacing.lg,
  },
  totalText: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.semibold,
  },
  totalAmount: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    fontFamily: typography.fontFamily.bold,
  },
  confirmButton: {
    width: '100%',
  },
});
