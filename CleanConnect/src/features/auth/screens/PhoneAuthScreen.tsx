import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { colors } from "@/constants/colors";
import { spacing } from "@/constants/spacing";
import { typography, textStyles } from "@/constants/typography";
import { Button } from "../../../components/common/Button";
import { auth } from "../../../lib/supabase";
import { useAuthStore } from "../../../store/useAuthStore";

export const PhoneAuthScreen: React.FC = () => {
  const [phone, setPhone] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [loading, setLoading] = useState(false);
  const [timer, setTimer] = useState(60);
  const [canResend, setCanResend] = useState(false);
  const { login } = useAuthStore();

  const otpRefs = useRef<TextInput[]>([]);

  // Timer effect for OTP resend
  useEffect(() => {
    let interval: ReturnType<typeof setInterval>;
    if (isOtpSent && timer > 0) {
      interval = setInterval(() => {
        setTimer((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isOtpSent, timer]);

  const handleSendOtp = async () => {
    if (!phone || phone.length < 10) {
      Alert.alert("Error", "Please enter a valid 10-digit phone number");
      return;
    }

    // Format phone number with country code
    const formattedPhone = phone.startsWith("+91") ? phone : `+91${phone}`;

    setLoading(true);
    try {
      console.log("Sending OTP to phone:", formattedPhone);
      const { error } = await auth.signInWithPhone(formattedPhone);
      if (error) {
        console.error("OTP send error:", error);
        Alert.alert("Error", error.message);
      } else {
        setIsOtpSent(true);
        Alert.alert("Success", "OTP sent to your phone number");
      }
    } catch (error) {
      console.error("OTP send exception:", error);
      Alert.alert("Error", "Failed to send OTP. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleOtpChange = (value: string, index: number) => {
    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto-focus next input
    if (value && index < 5) {
      otpRefs.current[index + 1]?.focus();
    }
  };

  const handleOtpKeyPress = (key: string, index: number) => {
    if (key === "Backspace" && !otp[index] && index > 0) {
      otpRefs.current[index - 1]?.focus();
    }
  };

  const handleVerifyOtp = async () => {
    const otpString = otp.join("");
    if (!otpString || otpString.length !== 6) {
      Alert.alert("Error", "Please enter a valid 6-digit OTP");
      return;
    }

    // Format phone number with country code
    const formattedPhone = phone.startsWith("+91") ? phone : `+91${phone}`;

    setLoading(true);
    try {
      console.log("Verifying OTP:", otpString, "for phone:", formattedPhone);

      // Check if we're using placeholder Supabase config
      const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
      const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

      if (
        !supabaseUrl ||
        supabaseUrl === "https://placeholder.supabase.co" ||
        !supabaseKey ||
        supabaseKey === "placeholder-key"
      ) {
        // Development mode - simulate successful verification
        console.log("Using development mode - simulating OTP verification");
        const mockUser = {
          id: "user-123",
          phone: formattedPhone,
          email: null,
        } as any;

        await login(mockUser, "CUSTOMER");

        // Navigate to customer home screen
        router.replace("/customer/home");
        return;
      }

      // Verify OTP with Supabase
      const { data, error } = await auth.verifyOtp(formattedPhone, otpString);

      if (error) {
        console.error("OTP verification error:", error);
        Alert.alert("Error", error.message || "Invalid OTP. Please try again.");
      } else if (data?.user) {
        console.log("OTP verification successful:", data.user);
        // Set user data and role
        await login(data.user, "CUSTOMER");

        // Navigate to customer home screen
        router.replace("/customer/home");
      } else {
        console.error("OTP verification failed - no user data");
        Alert.alert("Error", "Verification failed. Please try again.");
      }
    } catch (error) {
      console.error("OTP verification exception:", error);
      Alert.alert("Error", "Invalid OTP. Please try again.");
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = () => {
    setIsOtpSent(false);
    setOtp(["", "", "", "", "", ""]);
    setTimer(60);
    setCanResend(false);
  };

  const renderPhoneInput = () => (
    <View style={styles.phoneContainer}>
      {/* Phone Icon */}
      <View style={styles.iconContainer}>
        <Ionicons name="call" size={48} color={colors.text.primary} />
      </View>

      {/* Title and Subtitle */}
      <Text style={styles.title}>Enter your phone number</Text>
      <Text style={styles.subtitle}>
        We&apos;ll send you a text with a verification code.
      </Text>

      {/* Phone Input with Country Code */}
      <View style={styles.phoneInputContainer}>
        <TouchableOpacity style={styles.countryCodeContainer}>
          <Text style={styles.countryCode}>+91</Text>
          <Ionicons name="chevron-down" size={16} color={colors.gray[600]} />
        </TouchableOpacity>

        <TextInput
          style={styles.phoneInput}
          placeholder="784271000"
          value={phone}
          onChangeText={(text) => {
            // Only allow digits and limit to 10 characters
            const cleanedText = text.replace(/[^0-9]/g, "").slice(0, 10);
            setPhone(cleanedText);
          }}
          keyboardType="phone-pad"
          maxLength={10}
          placeholderTextColor={colors.gray[400]}
        />
      </View>

      {/* Continue Button */}
      <Button
        title="Continue"
        onPress={handleSendOtp}
        loading={loading}
        style={styles.continueButton}
      />

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          By continuing, you agree to our{" "}
          <Text style={styles.linkText}>T&C</Text> and{" "}
          <Text style={styles.linkText}>Privacy</Text> policy
        </Text>
      </View>
    </View>
  );

  const renderOtpInput = () => (
    <View style={styles.otpContainer}>
      {/* Back Button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setIsOtpSent(false)}
      >
        <Ionicons name="arrow-back" size={24} color={colors.text.primary} />
      </TouchableOpacity>

      {/* OTP Icon */}
      <View style={styles.iconContainer}>
        <Ionicons name="chatbox" size={48} color={colors.text.primary} />
      </View>

      {/* Title and Subtitle */}
      <Text style={styles.title}>Enter verification code</Text>
      <Text style={styles.subtitle}>
        A 6-digit verification code has been sent on{"\n"}
        +91 {phone}
      </Text>

      {/* OTP Input Boxes */}
      <View style={styles.otpInputContainer}>
        {otp.map((digit, index) => (
          <TextInput
            key={index}
            ref={(ref) => {
              if (ref) otpRefs.current[index] = ref;
            }}
            style={[
              styles.otpBox,
              digit ? styles.otpBoxFilled : null,
              index === 0 ? styles.otpBoxFirst : null,
            ]}
            value={digit}
            onChangeText={(value) => handleOtpChange(value, index)}
            onKeyPress={({ nativeEvent }) =>
              handleOtpKeyPress(nativeEvent.key, index)
            }
            keyboardType="numeric"
            maxLength={1}
            textAlign="center"
          />
        ))}
      </View>

      {/* Timer */}
      <View style={styles.timerContainer}>
        <Ionicons name="time" size={16} color={colors.gray[600]} />
        <Text style={styles.timerText}>
          {Math.floor(timer / 60)}:{(timer % 60).toString().padStart(2, "0")}
        </Text>
      </View>

      {/* Verify Button */}
      <Button
        title="Verify"
        onPress={handleVerifyOtp}
        loading={loading}
        style={styles.verifyButton}
      />

      {/* Resend Option */}
      {canResend && (
        <TouchableOpacity
          onPress={handleResendOtp}
          style={styles.resendContainer}
        >
          <Text style={styles.resendText}>
            Didn&apos;t receive code? Resend
          </Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={colors.white} />
      <KeyboardAvoidingView
        style={styles.keyboardContainer}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
      >
        <View style={styles.content}>
          {!isOtpSent ? renderPhoneInput() : renderOtpInput()}
        </View>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  },
  keyboardContainer: {
    flex: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    justifyContent: "center",
  },

  // Phone Input Screen Styles
  phoneContainer: {
    alignItems: "center",
  },
  iconContainer: {
    marginBottom: spacing.xxl,
  },
  title: {
    ...textStyles.h2,
    color: colors.text.primary,
    textAlign: "center",
    marginBottom: spacing.md,
  },
  subtitle: {
    ...textStyles.body,
    color: colors.text.tertiary,
    textAlign: "center",
    marginBottom: spacing.xxxl,
    lineHeight: typography.sizes.md * typography.lineHeights.normal,
  },
  phoneInputContainer: {
    flexDirection: "row",
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: 12,
    marginBottom: spacing.xxxl,
    backgroundColor: colors.white,
  },
  countryCodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    borderRightWidth: 1,
    borderRightColor: colors.border.light,
    minWidth: 80,
  },
  countryCode: {
    ...textStyles.body,
    color: colors.text.primary,
    marginRight: spacing.xs,
  },
  phoneInput: {
    flex: 1,
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.lg,
    ...textStyles.body,
    color: colors.text.primary,
  },
  continueButton: {
    width: "100%",
    marginBottom: spacing.xxxl,
  },

  // OTP Input Screen Styles
  otpContainer: {
    alignItems: "center",
    position: "relative",
  },
  backButton: {
    position: "absolute",
    top: 0,
    left: 0,
    padding: spacing.sm,
  },
  otpInputContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  },
  otpBox: {
    width: 48,
    height: 48,
    borderWidth: 1,
    borderColor: colors.border.light,
    borderRadius: 8,
    marginHorizontal: spacing.xs,
    ...textStyles.body,
    color: colors.text.primary,
    backgroundColor: colors.white,
  },
  otpBoxFilled: {
    borderColor: colors.primary,
    backgroundColor: colors.background.secondary,
  },
  otpBoxFirst: {
    borderColor: colors.primary,
  },
  timerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: spacing.xl,
  },
  timerText: {
    ...textStyles.body,
    color: colors.text.tertiary,
    marginLeft: spacing.xs,
  },
  verifyButton: {
    width: "100%",
    marginBottom: spacing.lg,
  },
  resendContainer: {
    padding: spacing.md,
  },
  resendText: {
    ...textStyles.body,
    color: colors.primary,
    textAlign: "center",
  },

  // Footer Styles
  footer: {
    alignItems: "center",
    marginTop: spacing.xl,
  },
  footerText: {
    ...textStyles.bodySmall,
    color: colors.text.tertiary,
    textAlign: "center",
    lineHeight: typography.sizes.sm * typography.lineHeights.normal,
  },
  linkText: {
    color: colors.primary,
    textDecorationLine: "underline",
  },
});
