import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  Dimensions,
  FlatList,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Button } from '../../../components/common/Button';
import { useAuthStore } from '../../../store/useAuthStore';
import { onboardingStyles } from '../styles/OnboardingScreen.styles';

// Define onboarding screens content
const onboardingScreens = [
  {
    id: '1',
    title: 'Professional Home Services',
    description: 'CleanConnect offers a wide range of professional cleaning and home services tailored to your needs.',
    image: 'https://images.unsplash.com/photo-1581578731548-c64695cc6952?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'home-outline',
  },
  {
    id: '2',
    title: 'Real-time Provider Connection',
    description: 'Connect with service providers in real-time and track their arrival at your doorstep.',
    image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'time-outline',
  },
  {
    id: '3',
    title: 'Safe & Trusted Providers',
    description: 'All our service providers are vetted, background-checked, and trained to deliver quality service.',
    image: 'https://images.unsplash.com/photo-**********-e15b29be8c8f?ixlib=rb-1.2.1&auto=format&fit=crop&w=1350&q=80',
    icon: 'shield-checkmark-outline',
  },
];

interface RoleOption {
  id: 'CUSTOMER' | 'PROVIDER';
  title: string;
  description: string;
  icon: string;
}

const roleOptions: RoleOption[] = [
  {
    id: 'CUSTOMER',
    title: 'I need cleaning services',
    description: 'Book professional cleaning and home services',
    icon: '🏠',
  },
  {
    id: 'PROVIDER',
    title: 'I provide cleaning services',
    description: 'Offer your services and connect with customers',
    icon: '🧹',
  },
];

/**
 * OnboardingScreen Component
 *
 * Handles the onboarding flow for new users with a 3-screen walkthrough
 * explaining the app's features and benefits, followed by role selection.
 */
export const OnboardingScreen: React.FC = () => {
  const flatListRef = useRef<FlatList>(null);
  const { setRole } = useAuthStore();

  // State management
  const [currentIndex, setCurrentIndex] = useState(0);
  const [selectedRole, setSelectedRole] = useState<'CUSTOMER' | 'PROVIDER' | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [showRoleSelection, setShowRoleSelection] = useState(false);

  // Get screen dimensions for responsive layout
  const { width } = Dimensions.get('window');

  // Handle skip button press
  const handleSkip = useCallback(async () => {
    try {
      setShowRoleSelection(true);
    } catch (error) {
      console.error('Error during skip:', error);
    }
  }, []);

  // Handle next button press
  const handleNext = useCallback(async () => {
    if (currentIndex < onboardingScreens.length - 1) {
      flatListRef.current?.scrollToIndex({
        index: currentIndex + 1,
        animated: true,
      });
    } else {
      setShowRoleSelection(true);
    }
  }, [currentIndex]);

  // Handle role selection
  const handleRoleSelect = useCallback((role: 'CUSTOMER' | 'PROVIDER') => {
    setSelectedRole(role);
  }, []);

  // Handle get started button press
  const handleGetStarted = useCallback(async () => {
    if (!selectedRole) return;

    setIsLoading(true);

    try {
      // Set role in store
      setRole(selectedRole);

      // Navigate to appropriate screen based on role with a slight delay for better UX
      setTimeout(() => {
        if (selectedRole === 'CUSTOMER') {
          router.replace('/auth/phone');
        } else {
          router.replace('/auth/email');
        }
        setIsLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error during get started:', error);
      setIsLoading(false);
    }
  }, [selectedRole, setRole]);

  // Handle scroll event to update current index
  const handleViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      setCurrentIndex(viewableItems[0].index);
    }
  }, []);

  const viewabilityConfig = useMemo(() => ({
    itemVisiblePercentThreshold: 50,
  }), []);

  // Render onboarding screen item
  const renderItem = useCallback(({ item }: any) => {
    return (
      <View style={[onboardingStyles.slide, { width }]}>
        <View style={onboardingStyles.imageContainer}>
          <Image
            source={{ uri: item.image }}
            style={onboardingStyles.image}
            resizeMode="cover"
          />
          <View style={onboardingStyles.iconOverlay}>
            <Ionicons name={item.icon} size={32} color="white" />
          </View>
        </View>
        <View style={onboardingStyles.textContainer}>
          <Text style={onboardingStyles.title}>{item.title}</Text>
          <Text style={onboardingStyles.description}>{item.description}</Text>
        </View>
      </View>
    );
  }, [width]);

  // Render role selection screen
  const renderRoleSelection = useCallback(() => (
    <View style={onboardingStyles.roleSelection}>
      {/* Illustration */}
      <View style={onboardingStyles.illustrationContainer}>
        <View style={onboardingStyles.illustration}>
          <Text style={onboardingStyles.illustrationEmoji}>👥</Text>
        </View>
      </View>

      {/* Title and Subtitle */}
      <View style={onboardingStyles.roleTextContainer}>
        <Text style={onboardingStyles.roleTitle}>How can we help you?</Text>
        <Text style={onboardingStyles.roleSubtitle}>
          Choose your role to get started with CleanConnect
        </Text>
      </View>

      {/* Role Options */}
      <View style={onboardingStyles.roleOptionsContainer}>
        {roleOptions.map((role) => (
          <TouchableOpacity
            key={role.id}
            style={[
              onboardingStyles.roleOption,
              selectedRole === role.id && onboardingStyles.roleOptionSelected,
            ]}
            onPress={() => handleRoleSelect(role.id)}
            activeOpacity={0.7}
          >
            <Text style={[
              onboardingStyles.roleOptionText,
              selectedRole === role.id && onboardingStyles.roleOptionTextSelected,
            ]}>
              {role.title}
            </Text>
            {selectedRole === role.id && (
              <View style={onboardingStyles.checkmark}>
                <Ionicons name="checkmark" size={16} color="white" />
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>
    </View>
  ), [selectedRole, handleRoleSelect]);

  // Render pagination dots
  const renderPagination = useCallback(() => {
    if (showRoleSelection) return null;

    return (
      <View style={onboardingStyles.pagination}>
        {onboardingScreens.map((_, index) => (
          <View
            key={index}
            style={[
              onboardingStyles.paginationDot,
              { backgroundColor: index === currentIndex ? onboardingStyles.paginationDotActive.backgroundColor : onboardingStyles.paginationDot.backgroundColor }
            ]}
          />
        ))}
      </View>
    );
  }, [currentIndex, showRoleSelection]);

  if (showRoleSelection) {
    return (
      <SafeAreaView style={onboardingStyles.container}>
        <StatusBar barStyle="dark-content" />

        {/* Header with back button and progress */}
        <View style={onboardingStyles.header}>
          <TouchableOpacity style={onboardingStyles.backButton} onPress={() => setShowRoleSelection(false)}>
            <Ionicons name="chevron-back" size={24} color="#666" />
          </TouchableOpacity>
        </View>

        {/* Progress Bar */}
        <View style={onboardingStyles.progressContainer}>
          <View style={onboardingStyles.progressBar}>
            <View style={[onboardingStyles.progressFill, { width: '100%' }]} />
          </View>
        </View>

        {renderRoleSelection()}

        {/* Footer with continue button */}
        <View style={onboardingStyles.footer}>
          <Button
            title={isLoading ? "Loading..." : "Continue"}
            onPress={handleGetStarted}
            disabled={!selectedRole || isLoading}
            style={onboardingStyles.getStartedButton}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={onboardingStyles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header with Skip button */}
      <View style={onboardingStyles.header}>
        <TouchableOpacity style={onboardingStyles.skipButton} onPress={handleSkip}>
          <Text style={onboardingStyles.skipText}>Skip</Text>
        </TouchableOpacity>
      </View>

      {/* Onboarding Slides */}
      <FlatList
        ref={flatListRef}
        data={onboardingScreens}
        renderItem={renderItem}
        keyExtractor={(item) => item.id}
        horizontal
        pagingEnabled
        showsHorizontalScrollIndicator={false}
        onViewableItemsChanged={handleViewableItemsChanged}
        viewabilityConfig={viewabilityConfig}
      />

      {/* Pagination Dots */}
      {renderPagination()}

      {/* Footer with buttons */}
      <View style={onboardingStyles.footer}>
        {currentIndex === onboardingScreens.length - 1 ? (
          <Button
            title="Choose Role"
            onPress={handleNext}
            style={onboardingStyles.button}
          />
        ) : (
          <Button
            title="Next"
            onPress={handleNext}
            style={onboardingStyles.button}
          />
        )}
      </View>
    </SafeAreaView>
  );
};