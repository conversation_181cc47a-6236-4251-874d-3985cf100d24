import { StyleSheet, Dimensions, ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

const { width } = Dimensions.get('window');

export const onboardingStyles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
  } as ViewStyle,

  header: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    // paddingHorizontal: spacing.xl,
    paddingTop: spacing.lg,
    paddingBottom: spacing.md,
  } as ViewStyle,

  skipButton: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  } as ViewStyle,

  skipText: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontWeight: typography.weights.medium,
    fontFamily: typography.fontFamily.medium,
  } as TextStyle,

  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.gray[100],
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  slide: {
    width,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.xl,
  } as ViewStyle,

  imageContainer: {
    width: 280,
    height: 280,
    borderRadius: 20,
    marginBottom: spacing.xl,
    position: 'relative',
    overflow: 'hidden',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  } as ViewStyle,

  image: {
    width: '100%',
    height: '100%',
    borderRadius: 10,
  } as ImageStyle,

  iconOverlay: {
    position: 'absolute',
    bottom: spacing.md,
    right: spacing.md,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  } as ViewStyle,

  textContainer: {
    alignItems: 'center',
    paddingHorizontal: spacing.md,
  } as ViewStyle,
  title: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    textAlign: 'center',
    marginBottom: spacing.md,
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  description: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    textAlign: 'center',
    // lineHeight: typography.lineHeights.relaxed,
    paddingHorizontal: spacing.md,
    fontFamily: typography.fontFamily.regular,
  } as TextStyle,

  pagination: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: spacing.md,
  } as ViewStyle,

  paginationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: colors.gray[300],
    marginHorizontal: spacing.xs,
  } as ViewStyle,

  paginationDotActive: {
    backgroundColor: colors.primary,
    width: 24,
  } as ViewStyle,
  roleSelection: {
    flex: 1,
    paddingHorizontal: spacing.xl,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  illustrationContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
  } as ViewStyle,

  illustration: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: colors.primary + '15',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: spacing.lg,
  } as ViewStyle,

  illustrationEmoji: {
    fontSize: 48,
  } as TextStyle,

  roleTextContainer: {
    alignItems: 'center',
    marginBottom: spacing.xl,
    paddingHorizontal: spacing.md,
  } as ViewStyle,

  roleTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    textAlign: 'center',
    marginBottom: spacing.sm,
    fontFamily: typography.fontFamily.bold,
    lineHeight: typography.sizes.xxl * typography.lineHeights.tight,
  } as TextStyle,

  roleSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    textAlign: 'center',
    fontFamily: typography.fontFamily.regular,
    // lineHeight: typography.sizes.md * typography.lineHeights.relaxed,
    maxWidth: 280,
  } as TextStyle,

  roleOptionsContainer: {
    width: '100%',
    gap: spacing.md,
  } as ViewStyle,

  roleOption: {
    backgroundColor: colors.gray[100],
    borderRadius: 16,
    paddingVertical: spacing.lg,
    paddingHorizontal: spacing.xl,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
    minHeight: 60,
  } as ViewStyle,

  roleOptionSelected: {
    backgroundColor: colors.warning,
    borderColor: colors.primary,
  } as ViewStyle,

  roleOptionText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.medium,
    color: colors.gray[700],
    fontFamily: typography.fontFamily.medium,
    // lineHeight: typography.sizes.md * typography.lineHeights.normal,
  } as TextStyle,

  roleOptionTextSelected: {
    color: colors.white,
  } as TextStyle,

  checkmark: {
    position: 'absolute',
    right: spacing.lg,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: colors.white + '30',
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,



  progressContainer: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.lg,
  } as ViewStyle,

  progressBar: {
    height: 4,
    backgroundColor: colors.gray[200],
    borderRadius: 2,
    overflow: 'hidden',
  } as ViewStyle,

  progressFill: {
    height: '100%',
    backgroundColor: colors.primary,
    borderRadius: 2,
  } as ViewStyle,

  footer: {
    paddingHorizontal: spacing.xl,
    paddingBottom: spacing.xl,
    backgroundColor: colors.white,
  } as ViewStyle,

  button: {
    width: '100%',
    marginTop: spacing.md,
  } as ViewStyle,

  getStartedButton: {
    width: '100%',
    marginTop: spacing.md,
  } as ViewStyle,
}); 