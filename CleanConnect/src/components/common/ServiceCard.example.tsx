import React from 'react';
import { View, ScrollView } from 'react-native';
import { ServiceCard } from './ServiceCard';

// Example usage of the ServiceCard component
export const ServiceCardExample: React.FC = () => {
  const handleServicePress = (serviceId: string) => {
    console.log('Service pressed:', serviceId);
  };

  const handleAddPress = (serviceId: string) => {
    console.log('Add pressed:', serviceId);
  };

  return (
    <ScrollView style={{ padding: 16 }}>
      {/* Modern variant */}
      <ServiceCard
        id="1"
        name="House Cleaning"
        description="Complete house cleaning including kitchen, bathroom, and living areas"
        price={89}
        originalPrice={99}
        duration="2-3 hours"
        icon="🏠"
        popular={true}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />

      {/* Compact variant */}
      <ServiceCard
        id="2"
        name="Office Cleaning"
        description="Professional office cleaning service"
        price={120}
        duration="1-2 hours"
        icon="🏢"
        variant="compact"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
        showAddButton={false}
      />

      {/* Modern with image */}
      <ServiceCard
        id="3"
        name="Deep Cleaning"
        description="Comprehensive deep cleaning service for your entire home"
        price={150}
        originalPrice={180}
        duration="4-5 hours"
        image="✨"
        note="Includes all cleaning supplies"
        popular={true}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />

      {/* Unavailable service */}
      <ServiceCard
        id="4"
        name="Carpet Cleaning"
        description="Professional carpet and upholstery cleaning"
        price={75}
        duration="1-2 hours"
        icon="🧽"
        available={false}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />
    </ScrollView>
  );
};
