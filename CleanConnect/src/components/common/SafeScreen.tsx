import React from 'react';
import { View, ViewStyle } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { colors } from '@/constants/colors';

interface SafeScreenProps {
  children: React.ReactNode;
  backgroundColor?: string;
  style?: ViewStyle;
  edges?: ('top' | 'bottom' | 'left' | 'right')[];
}

const SafeScreen: React.FC<SafeScreenProps> = ({ 
  children, 
  backgroundColor = colors.white,
  style,
  edges = ['top']
}) => {
  const insets = useSafeAreaInsets();

  const paddingStyle = {
    paddingTop: edges.includes('top') ? insets.top : 0,
    paddingBottom: edges.includes('bottom') ? insets.bottom : 0,
    paddingLeft: edges.includes('left') ? insets.left : 0,
    paddingRight: edges.includes('right') ? insets.right : 0,
  };

  return (
    <View
      style={[
        {
          flex: 1,
          backgroundColor,
          ...paddingStyle,
        },
        style,
      ]}
    >
      {children}
    </View>
  );
};

export default SafeScreen;
