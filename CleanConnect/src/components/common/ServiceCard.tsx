import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ViewStyle,
  TextStyle,
  Dimensions,
} from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';

const { width: screenWidth } = Dimensions.get('window');

export interface ServiceCardProps {
  id: string;
  name: string;
  description: string;
  price: number;
  originalPrice?: number;
  duration?: string;
  icon?: string;
  image?: string;
  note?: string;
  popular?: boolean;
  available?: boolean;
  rating?: number;
  reviewCount?: number;
  features?: string[];
  onPress?: (serviceId: string) => void;
  onAddPress?: (serviceId: string) => void;
  style?: ViewStyle;
  variant?: 'default' | 'compact' | 'detailed' | 'modern';
  showAddButton?: boolean;
  cardWidth?: number;
}

export const ServiceCard: React.FC<ServiceCardProps> = ({
  id,
  name,
  description,
  price,
  originalPrice,
  duration,
  icon = '🧹',
  image,
  note,
  popular = false,
  available = true,
  rating: _rating,
  reviewCount: _reviewCount,
  features: _features = [],
  onPress,
  onAddPress,
  style,
  variant = 'modern',
  showAddButton = true,
  cardWidth,
}) => {
  const handlePress = () => {
    if (onPress && available) {
      onPress(id);
    }
  };

  const handleAddPress = () => {
    if (onAddPress && available) {
      onAddPress(id);
    }
  };

  // Calculate responsive card width
  const getCardWidth = () => {
    if (cardWidth) return cardWidth;

    switch (variant) {
      case 'compact':
        return (screenWidth - spacing.layout.container * 2 - spacing.md) / 2;
      case 'modern':
        return screenWidth - spacing.layout.container * 2;
      default:
        return screenWidth - spacing.layout.container * 2;
    }
  };

  const renderModernCard = () => (
    <TouchableOpacity
      style={[
        styles.modernCard,
        { width: getCardWidth() },
        !available && styles.unavailableCard,
        style,
      ]}
      onPress={handlePress}
      disabled={!available}
      activeOpacity={0.8}
    >
      {/* Service Image */}
      <View style={styles.imageContainer}>
        {image ? (
          <Text style={styles.serviceImage}>{image}</Text>
        ) : (
          <Text style={styles.serviceIcon}>{icon}</Text>
        )}
        {popular && (
          <View style={styles.popularBadge}>
            <Text style={styles.popularText}>Popular</Text>
          </View>
        )}
      </View>

      {/* Service Content */}
      <View style={styles.contentContainer}>
        <Text style={styles.serviceName} numberOfLines={2}>
          {name}
        </Text>

        <Text style={styles.serviceDescription} numberOfLines={3}>
          {description}
        </Text>

        {duration && (
          <Text style={styles.serviceDuration}>
            Duration: {duration}
          </Text>
        )}

        {note && (
          <Text style={styles.serviceNote} numberOfLines={2}>
            Note: {note}
          </Text>
        )}

        {/* Price and Add Button Row */}
        <View style={styles.bottomRow}>
          <View style={styles.priceContainer}>
            <Text style={styles.currentPrice}>₹{price}</Text>
            {originalPrice && originalPrice > price && (
              <Text style={styles.originalPrice}>₹{originalPrice}</Text>
            )}
          </View>

          {showAddButton && (
            <TouchableOpacity
              style={[
                styles.addButton,
                !available && styles.addButtonDisabled
              ]}
              onPress={handleAddPress}
              disabled={!available}
              activeOpacity={0.8}
            >
              <Text style={[
                styles.addButtonText,
                !available && styles.addButtonTextDisabled
              ]}>
                {available ? 'Add' : 'N/A'}
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );

  const renderCompactCard = () => (
    <TouchableOpacity
      style={[
        styles.compactCard,
        { width: getCardWidth() },
        !available && styles.unavailableCard,
        style,
      ]}
      onPress={handlePress}
      disabled={!available}
      activeOpacity={0.8}
    >
      <View style={styles.compactContent}>
        <View style={styles.compactIconContainer}>
          <Text style={styles.compactIcon}>{icon}</Text>
        </View>
        <Text style={styles.compactName} numberOfLines={2}>
          {name}
        </Text>
        {popular && (
          <View style={styles.compactPopularBadge}>
            <Text style={styles.popularText}>Popular</Text>
          </View>
        )}
      </View>
    </TouchableOpacity>
  );

  // Return the appropriate variant
  switch (variant) {
    case 'compact':
      return renderCompactCard();
    case 'modern':
      return renderModernCard();
    default:
      return renderModernCard(); // Default to modern design
  }
};

const styles = StyleSheet.create({
  // Modern Card Styles
  modernCard: {
    backgroundColor: colors.white,
    borderRadius: 16,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
    marginBottom: spacing.lg,
    overflow: 'hidden',
  } as ViewStyle,

  imageContainer: {
    height: 180,
    backgroundColor: colors.gray[50],
    justifyContent: 'center',
    alignItems: 'center',
    position: 'relative',
  } as ViewStyle,

  serviceImage: {
    fontSize: 80,
    textAlign: 'center',
  } as TextStyle,

  serviceIcon: {
    fontSize: 60,
    textAlign: 'center',
  } as TextStyle,

  contentContainer: {
    padding: spacing.lg,
  } as ViewStyle,

  serviceName: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.lg * typography.lineHeights.tight,
  } as TextStyle,

  serviceDescription: {
    fontSize: typography.sizes.sm,
    color: colors.text.secondary,
    fontFamily: typography.fontFamily.regular,
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.sm * typography.lineHeights.normal,
  } as TextStyle,

  serviceDuration: {
    fontSize: typography.sizes.sm,
    color: colors.text.tertiary,
    fontFamily: typography.fontFamily.medium,
    marginBottom: spacing.xs,
  } as TextStyle,

  serviceNote: {
    fontSize: typography.sizes.sm,
    color: colors.primary,
    fontFamily: typography.fontFamily.medium,
    marginBottom: spacing.md,
    lineHeight: typography.sizes.sm * typography.lineHeights.normal,
  } as TextStyle,

  bottomRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: spacing.sm,
  } as ViewStyle,

  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: spacing.xs,
  } as ViewStyle,

  currentPrice: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.bold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.bold,
  } as TextStyle,

  originalPrice: {
    fontSize: typography.sizes.md,
    color: colors.text.tertiary,
    fontFamily: typography.fontFamily.regular,
    textDecorationLine: 'line-through',
  } as TextStyle,

  addButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.md,
    borderRadius: 12,
    minWidth: 80,
    alignItems: 'center',
    justifyContent: 'center',
  } as ViewStyle,

  addButtonDisabled: {
    backgroundColor: colors.gray[300],
  } as ViewStyle,

  addButtonText: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.white,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  addButtonTextDisabled: {
    color: colors.gray[500],
  } as TextStyle,

  popularBadge: {
    position: 'absolute',
    top: spacing.md,
    right: spacing.md,
    backgroundColor: colors.warning,
    paddingHorizontal: spacing.sm,
    paddingVertical: spacing.xs,
    borderRadius: 12,
    zIndex: 1,
  } as ViewStyle,

  popularText: {
    fontSize: typography.sizes.xs,
    color: colors.white,
    fontWeight: typography.weights.semibold,
    fontFamily: typography.fontFamily.semibold,
  } as TextStyle,

  unavailableCard: {
    opacity: 0.6,
  } as ViewStyle,

  // Compact Card Styles
  compactCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    shadowColor: colors.shadow.medium,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: spacing.md,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
  } as ViewStyle,

  compactContent: {
    alignItems: 'center',
    justifyContent: 'center',
    height: '100%',
    padding: spacing.md,
  } as ViewStyle,

  compactIconContainer: {
    marginBottom: spacing.md,
    alignItems: 'center',
    justifyContent: 'center',
    width: 60,
    height: 60,
    borderRadius: 30,
    backgroundColor: colors.primary + '20',
  } as ViewStyle,

  compactIcon: {
    fontSize: 32,
    textAlign: 'center',
  } as TextStyle,

  compactName: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.text.primary,
    fontFamily: typography.fontFamily.semibold,
    textAlign: 'center',
    marginBottom: spacing.sm,
    lineHeight: typography.sizes.md * typography.lineHeights.tight,
  } as TextStyle,

  compactPopularBadge: {
    position: 'absolute',
    top: spacing.sm,
    right: spacing.sm,
    backgroundColor: colors.warning,
    paddingHorizontal: spacing.xs,
    paddingVertical: 2,
    borderRadius: 8,
  } as ViewStyle,
});
