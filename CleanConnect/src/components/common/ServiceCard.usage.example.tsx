import React from 'react';
import { View, ScrollView, StyleSheet } from 'react-native';
import { ServiceCard } from './ServiceCard';
import { spacing } from '@/constants/spacing';

/**
 * Example usage of the improved ServiceCard component
 * This demonstrates how to use the new modern design that matches
 * the provided visual reference with responsive sizing
 */
export const ServiceCardUsageExample: React.FC = () => {
  const handleServicePress = (serviceId: string) => {
    console.log('Service pressed:', serviceId);
  };

  const handleAddPress = (serviceId: string) => {
    console.log('Add to cart pressed:', serviceId);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      
      {/* Modern Card - Full Width (Default) */}
      <ServiceCard
        id="1"
        name="Sink block removal"
        description="Remove sink blockages efficiently with our expert plumbers ensure smooth water flow for optimal si..."
        price={109}
        originalPrice={119}
        duration="30 Minutes"
        image="🚿" // Using emoji as image placeholder
        note="Spare parts replacement and purchase of ..."
        popular={false}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />

      {/* Another Modern Card Example */}
      <ServiceCard
        id="2"
        name="Deep House Cleaning"
        description="Complete deep cleaning service for your entire home including kitchen, bathrooms, and all living areas"
        price={199}
        originalPrice={249}
        duration="4-5 Hours"
        image="🏠"
        note="All cleaning supplies included in the service"
        popular={true}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />

      {/* Compact Cards in a Row */}
      <View style={styles.compactRow}>
        <ServiceCard
          id="3"
          name="Bathroom Cleaning"
          description="Professional bathroom cleaning"
          price={75}
          duration="1 Hour"
          icon="🚿"
          variant="compact"
          onPress={handleServicePress}
          onAddPress={handleAddPress}
          showAddButton={false}
          cardWidth={160}
        />
        
        <ServiceCard
          id="4"
          name="Kitchen Cleaning"
          description="Complete kitchen deep clean"
          price={89}
          duration="1.5 Hours"
          icon="🍳"
          popular={true}
          variant="compact"
          onPress={handleServicePress}
          onAddPress={handleAddPress}
          showAddButton={false}
          cardWidth={160}
        />
      </View>

      {/* Unavailable Service Example */}
      <ServiceCard
        id="5"
        name="Carpet Cleaning"
        description="Professional carpet and upholstery cleaning service with eco-friendly products"
        price={150}
        originalPrice={180}
        duration="2-3 Hours"
        image="🧽"
        note="Currently unavailable in your area"
        available={false}
        variant="modern"
        onPress={handleServicePress}
        onAddPress={handleAddPress}
      />

    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: spacing.layout.container,
    backgroundColor: '#F9F9F9',
  },
  compactRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: spacing.lg,
  },
});

/**
 * Key Features of the Improved ServiceCard:
 * 
 * 1. **Responsive Design**: Automatically adjusts width based on variant and screen size
 * 2. **Modern Layout**: Clean design with image at top, content below, and action button
 * 3. **Flexible Content**: Supports image/icon, title, description, duration, notes
 * 4. **Pricing Display**: Shows current price and optional original price with strikethrough
 * 5. **Interactive Elements**: Separate handlers for card press and add button press
 * 6. **Visual States**: Popular badge, unavailable state, pressed states
 * 7. **Typography**: Uses design system typography with proper line heights
 * 8. **Colors**: Follows design system color scheme
 * 9. **Variants**: Modern (full-width) and Compact (for grids) variants
 * 10. **Accessibility**: Proper touch targets and disabled states
 * 
 * Usage Tips:
 * - Use 'modern' variant for detailed service listings
 * - Use 'compact' variant for service grids (2-3 columns)
 * - Set cardWidth for custom sizing in grids
 * - Use image prop for emoji/icon representations
 * - originalPrice creates strikethrough pricing effect
 * - note prop displays additional service information
 */
