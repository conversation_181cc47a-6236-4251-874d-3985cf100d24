import { create } from 'zustand';

// Import types only to avoid initialization issues
export interface Notification {
  id: string;
  type: 'booking_update' | 'message' | 'payment' | 'reminder' | 'promotion';
  title: string;
  message: string;
  data?: any;
  read: boolean;
  createdAt: string;
}

export interface BookingUpdate {
  id: string;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  progress?: number;
  message?: string;
  timestamp: string;
}

export interface ChatMessage {
  id: string;
  bookingId: string;
  senderId: string;
  senderType: 'customer' | 'provider';
  message: string;
  timestamp: string;
}

export interface PushNotificationData {
  title: string;
  body: string;
  data?: any;
}

interface NotificationState {
  notifications: Notification[];
  unreadCount: number;
  isConnected: boolean;
  subscriptions: Record<string, any>;
  pushNotificationsEnabled: boolean;
  
  // Actions
  addNotification: (notification: Notification) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  clearNotifications: () => void;
  setConnected: (connected: boolean) => void;
  setPushNotificationsEnabled: (enabled: boolean) => void;
  
  // Real-time subscriptions
  subscribeToBookingUpdates: (bookingId: string, callback: (update: BookingUpdate) => void) => void;
  subscribeToChatMessages: (bookingId: string, callback: (message: ChatMessage) => void) => void;
  subscribeToUserNotifications: (userId: string, callback: (notification: Notification) => void) => void;
  unsubscribeFromBooking: (bookingId: string) => void;
  unsubscribeFromChat: (bookingId: string) => void;
  unsubscribeFromNotifications: (userId: string) => void;
  unsubscribeAll: () => void;
  
  // Push notification actions
  initializePushNotifications: (userId: string) => Promise<boolean>;
  sendPushNotification: (data: PushNotificationData) => Promise<boolean>;
  updateBadgeCount: (count: number) => Promise<void>;
  clearBadgeCount: () => Promise<void>;
  
  // Mock actions for development
  simulateBookingUpdate: (bookingId: string, update: BookingUpdate) => void;
  simulateNewMessage: (bookingId: string, message: ChatMessage) => void;
  simulateNewNotification: (notification: Notification) => void;
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isConnected: false,
  subscriptions: {},
  pushNotificationsEnabled: false,

  addNotification: (notification) => {
    set((state) => ({
      notifications: [notification, ...state.notifications],
      unreadCount: state.unreadCount + 1,
    }));
    
    // Update badge count
    get().updateBadgeCount(get().unreadCount + 1);
  },

  markAsRead: (notificationId) => {
    set((state) => ({
      notifications: state.notifications.map((notification) =>
        notification.id === notificationId
          ? { ...notification, read: true }
          : notification
      ),
      unreadCount: Math.max(0, state.unreadCount - 1),
    }));
    
    // Update badge count
    get().updateBadgeCount(Math.max(0, get().unreadCount - 1));
  },

  markAllAsRead: () => {
    set((state) => ({
      notifications: state.notifications.map((notification) => ({
        ...notification,
        read: true,
      })),
      unreadCount: 0,
    }));
    
    // Clear badge count
    get().clearBadgeCount();
  },

  clearNotifications: () => {
    set({
      notifications: [],
      unreadCount: 0,
    });
    
    // Clear badge count
    get().clearBadgeCount();
  },

  setConnected: (connected) => {
    set({ isConnected: connected });
  },

  setPushNotificationsEnabled: (enabled) => {
    set({ pushNotificationsEnabled: enabled });
  },

  subscribeToBookingUpdates: (bookingId, callback) => {
    const { subscriptions } = get();
    const key = `booking-${bookingId}`;

    // Unsubscribe if already subscribed
    if (subscriptions[key]) {
      get().unsubscribeFromBooking(bookingId);
    }

    // For now, just store the callback for mock functionality
    console.log('Subscribing to booking updates for:', bookingId);
    set({ subscriptions: { ...subscriptions, [key]: { callback, type: 'booking' } } });
  },

  subscribeToChatMessages: (bookingId, callback) => {
    const { subscriptions } = get();
    const key = `chat-${bookingId}`;

    // Unsubscribe if already subscribed
    if (subscriptions[key]) {
      get().unsubscribeFromChat(bookingId);
    }

    // For now, just store the callback for mock functionality
    console.log('Subscribing to chat messages for:', bookingId);
    set({ subscriptions: { ...subscriptions, [key]: { callback, type: 'chat' } } });
  },

  subscribeToUserNotifications: (userId, callback) => {
    const { subscriptions } = get();
    const key = `notifications-${userId}`;

    // Unsubscribe if already subscribed
    if (subscriptions[key]) {
      get().unsubscribeFromNotifications(userId);
    }

    // For now, just store the callback for mock functionality
    console.log('Subscribing to user notifications for:', userId);
    set({ subscriptions: { ...subscriptions, [key]: { callback, type: 'notifications' } } });
  },

  unsubscribeFromBooking: (bookingId) => {
    const { subscriptions } = get();
    const key = `booking-${bookingId}`;
    const subscription = subscriptions[key];

    if (subscription) {
      if (subscription.unsubscribe) {
        subscription.unsubscribe();
      }
      const newSubscriptions = { ...subscriptions };
      delete newSubscriptions[key];
      set({ subscriptions: newSubscriptions });
    }
  },

  unsubscribeFromChat: (bookingId) => {
    const { subscriptions } = get();
    const key = `chat-${bookingId}`;
    const subscription = subscriptions[key];

    if (subscription) {
      if (subscription.unsubscribe) {
        subscription.unsubscribe();
      }
      const newSubscriptions = { ...subscriptions };
      delete newSubscriptions[key];
      set({ subscriptions: newSubscriptions });
    }
  },

  unsubscribeFromNotifications: (userId) => {
    const { subscriptions } = get();
    const key = `notifications-${userId}`;
    const subscription = subscriptions[key];

    if (subscription) {
      if (subscription.unsubscribe) {
        subscription.unsubscribe();
      }
      const newSubscriptions = { ...subscriptions };
      delete newSubscriptions[key];
      set({ subscriptions: newSubscriptions });
    }
  },

  unsubscribeAll: () => {
    console.log('Unsubscribing from all notifications');
    // Clear subscriptions object
    set({ subscriptions: {} });
  },

  // Push notification actions (simplified for now)
  initializePushNotifications: async (userId: string) => {
    console.log('Initializing push notifications for user:', userId);
    set({ pushNotificationsEnabled: true });
    return true;
  },

  sendPushNotification: async (data: PushNotificationData) => {
    console.log('Sending push notification:', data.title);
    return true;
  },

  updateBadgeCount: async (count: number) => {
    console.log('Updating badge count to:', count);
  },

  clearBadgeCount: async () => {
    console.log('Clearing badge count');
  },

  // Mock actions for development
  simulateBookingUpdate: (bookingId, update) => {
    console.log('Simulating booking update for:', bookingId, update);
  },

  simulateNewMessage: (bookingId, message) => {
    console.log('Simulating new message for:', bookingId, message);
  },

  simulateNewNotification: (notification) => {
    get().addNotification(notification);
  },
})); 