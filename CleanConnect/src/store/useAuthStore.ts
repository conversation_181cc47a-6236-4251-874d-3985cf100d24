import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User } from '@supabase/supabase-js';
import { auth } from '../lib/supabase';

export type UserRole = 'CUSTOMER' | 'PROVIDER' | 'ADMIN';

interface AuthState {
  user: User | null;
  role: UserRole | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  isInitialized: boolean;
  setUser: (user: User | null) => void;
  setRole: (role: UserRole | null) => void;
  setAuthenticated: (isAuthenticated: boolean) => void;
  setLoading: (isLoading: boolean) => void;
  setInitialized: (isInitialized: boolean) => void;
  initializeAuth: () => Promise<void>;
  logout: () => Promise<void>;
  login: (user: User, role: UserRole) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      role: null,
      isAuthenticated: false,
      isLoading: true,
      isInitialized: false,

      setUser: (user) => set({ user }),
      setRole: (role) => set({ role }),
      setAuthenticated: (isAuthenticated) => set({ isAuthenticated }),
      setLoading: (isLoading) => set({ isLoading }),
      setInitialized: (isInitialized) => set({ isInitialized }),

      // Initialize authentication state on app start
      initializeAuth: async () => {
        try {
          set({ isLoading: true });

          // Check for existing session in Supabase
          const { data: { user }, error } = await auth.getCurrentUser();

          if (user && !error) {
            // User has valid session
            const currentState = get();
            set({
              user,
              isAuthenticated: true,
              // Keep the stored role if available, otherwise default to CUSTOMER
              role: currentState.role || 'CUSTOMER',
              isLoading: false,
              isInitialized: true
            });
          } else {
            // No valid session
            set({
              user: null,
              role: null,
              isAuthenticated: false,
              isLoading: false,
              isInitialized: true
            });
          }
        } catch (error) {
          console.error('Error initializing auth:', error);
          set({
            user: null,
            role: null,
            isAuthenticated: false,
            isLoading: false,
            isInitialized: true
          });
        }
      },

      // Login function to set user and persist session
      login: async (user: User, role: UserRole) => {
        set({
          user,
          role,
          isAuthenticated: true,
          isLoading: false
        });
      },

      // Logout function to clear session
      logout: async () => {
        try {
          // Sign out from Supabase
          await auth.signOut();

          // Clear local state
          set({
            user: null,
            role: null,
            isAuthenticated: false,
            isLoading: false
          });
        } catch (error) {
          console.error('Error during logout:', error);
          // Clear local state even if Supabase logout fails
          set({
            user: null,
            role: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AsyncStorage),
      // Only persist specific fields
      partialize: (state) => ({
        user: state.user,
        role: state.role,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);