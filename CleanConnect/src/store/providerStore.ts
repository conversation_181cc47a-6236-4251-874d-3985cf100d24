import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

export interface ServiceRequest {
  id: string;
  customerName: string;
  customerAvatar?: string;
  service: string;
  serviceType: 'Premium' | 'Standard' | 'Basic';
  date: string;
  time: string;
  duration: string;
  location: string;
  address: string;
  price: number;
  status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'IN_PROGRESS' | 'COMPLETED';
  description?: string;
  requirements?: string[];
  customerRating?: number;
  customerReviews?: number;
  image?: string;
  createdAt: string;
}

export interface ProviderEarnings {
  totalEarnings: number;
  currentMonth: number;
  lastMonth: number;
  thisWeek: number;
  today: number;
  completedJobs: number;
  pendingJobs: number;
  rating: number;
  totalReviews: number;
}

export interface ProviderNotification {
  id: string;
  type: 'BOOKING_REQUEST' | 'PAYMENT' | 'REVIEW' | 'SYSTEM' | 'BOOKING_UPDATE';
  title: string;
  message: string;
  customerName?: string;
  customerAvatar?: string;
  amount?: number;
  rating?: number;
  timestamp: string;
  isRead: boolean;
  actionRequired?: boolean;
  bookingId?: string;
}

export interface ProviderProfile {
  id: string;
  name: string;
  email: string;
  phone: string;
  avatar?: string;
  rating: number;
  totalReviews: number;
  completedJobs: number;
  joinedDate: string;
  services: string[];
  location: string;
  isVerified: boolean;
  isOnline: boolean;
}

interface ProviderState {
  // Profile
  profile: ProviderProfile | null;
  
  // Earnings
  earnings: ProviderEarnings;
  
  // Service Requests
  serviceRequests: ServiceRequest[];
  pendingRequests: ServiceRequest[];
  acceptedRequests: ServiceRequest[];
  completedRequests: ServiceRequest[];
  
  // Notifications
  notifications: ProviderNotification[];
  unreadNotificationsCount: number;
  
  // UI State
  isLoading: boolean;
  selectedPeriod: 'monthly' | 'weekly' | 'daily';
}

interface ProviderActions {
  // Profile actions
  setProfile: (profile: ProviderProfile) => void;
  updateProfile: (updates: Partial<ProviderProfile>) => void;
  
  // Earnings actions
  setEarnings: (earnings: ProviderEarnings) => void;
  updateEarnings: (updates: Partial<ProviderEarnings>) => void;
  
  // Service Request actions
  setServiceRequests: (requests: ServiceRequest[]) => void;
  addServiceRequest: (request: ServiceRequest) => void;
  updateServiceRequest: (id: string, updates: Partial<ServiceRequest>) => void;
  acceptServiceRequest: (id: string) => void;
  declineServiceRequest: (id: string) => void;
  
  // Notification actions
  setNotifications: (notifications: ProviderNotification[]) => void;
  addNotification: (notification: ProviderNotification) => void;
  markNotificationAsRead: (id: string) => void;
  markAllNotificationsAsRead: () => void;
  
  // UI actions
  setLoading: (loading: boolean) => void;
  setSelectedPeriod: (period: 'monthly' | 'weekly' | 'daily') => void;
  
  // Computed getters
  getPendingRequestsCount: () => number;
  getUnreadNotificationsCount: () => number;
}

const initialState: ProviderState = {
  profile: null,
  earnings: {
    totalEarnings: 0,
    currentMonth: 0,
    lastMonth: 0,
    thisWeek: 0,
    today: 0,
    completedJobs: 0,
    pendingJobs: 0,
    rating: 0,
    totalReviews: 0,
  },
  serviceRequests: [],
  pendingRequests: [],
  acceptedRequests: [],
  completedRequests: [],
  notifications: [],
  unreadNotificationsCount: 0,
  isLoading: false,
  selectedPeriod: 'monthly',
};

export const useProviderStore = create<ProviderState & ProviderActions>()(
  persist(
    (set, get) => ({
      ...initialState,
      
      // Profile actions
      setProfile: (profile) => set({ profile }),
      updateProfile: (updates) => set((state) => ({
        profile: state.profile ? { ...state.profile, ...updates } : null
      })),
      
      // Earnings actions
      setEarnings: (earnings) => set({ earnings }),
      updateEarnings: (updates) => set((state) => ({
        earnings: { ...state.earnings, ...updates }
      })),
      
      // Service Request actions
      setServiceRequests: (requests) => {
        const pending = requests.filter(r => r.status === 'PENDING');
        const accepted = requests.filter(r => r.status === 'ACCEPTED' || r.status === 'IN_PROGRESS');
        const completed = requests.filter(r => r.status === 'COMPLETED');
        
        set({
          serviceRequests: requests,
          pendingRequests: pending,
          acceptedRequests: accepted,
          completedRequests: completed,
        });
      },
      
      addServiceRequest: (request) => set((state) => {
        const newRequests = [...state.serviceRequests, request];
        const pending = newRequests.filter(r => r.status === 'PENDING');
        const accepted = newRequests.filter(r => r.status === 'ACCEPTED' || r.status === 'IN_PROGRESS');
        const completed = newRequests.filter(r => r.status === 'COMPLETED');
        
        return {
          serviceRequests: newRequests,
          pendingRequests: pending,
          acceptedRequests: accepted,
          completedRequests: completed,
        };
      }),
      
      updateServiceRequest: (id, updates) => set((state) => {
        const updatedRequests = state.serviceRequests.map(request =>
          request.id === id ? { ...request, ...updates } : request
        );
        
        const pending = updatedRequests.filter(r => r.status === 'PENDING');
        const accepted = updatedRequests.filter(r => r.status === 'ACCEPTED' || r.status === 'IN_PROGRESS');
        const completed = updatedRequests.filter(r => r.status === 'COMPLETED');
        
        return {
          serviceRequests: updatedRequests,
          pendingRequests: pending,
          acceptedRequests: accepted,
          completedRequests: completed,
        };
      }),
      
      acceptServiceRequest: (id) => {
        get().updateServiceRequest(id, { status: 'ACCEPTED' });
      },
      
      declineServiceRequest: (id) => {
        get().updateServiceRequest(id, { status: 'DECLINED' });
      },
      
      // Notification actions
      setNotifications: (notifications) => {
        const unreadCount = notifications.filter(n => !n.isRead).length;
        set({ notifications, unreadNotificationsCount: unreadCount });
      },
      
      addNotification: (notification) => set((state) => {
        const newNotifications = [notification, ...state.notifications];
        const unreadCount = newNotifications.filter(n => !n.isRead).length;
        return {
          notifications: newNotifications,
          unreadNotificationsCount: unreadCount,
        };
      }),
      
      markNotificationAsRead: (id) => set((state) => {
        const updatedNotifications = state.notifications.map(notification =>
          notification.id === id ? { ...notification, isRead: true } : notification
        );
        const unreadCount = updatedNotifications.filter(n => !n.isRead).length;
        return {
          notifications: updatedNotifications,
          unreadNotificationsCount: unreadCount,
        };
      }),
      
      markAllNotificationsAsRead: () => set((state) => ({
        notifications: state.notifications.map(n => ({ ...n, isRead: true })),
        unreadNotificationsCount: 0,
      })),
      
      // UI actions
      setLoading: (isLoading) => set({ isLoading }),
      setSelectedPeriod: (selectedPeriod) => set({ selectedPeriod }),
      
      // Computed getters
      getPendingRequestsCount: () => get().pendingRequests.length,
      getUnreadNotificationsCount: () => get().unreadNotificationsCount,
    }),
    {
      name: 'provider-storage',
      storage: createJSONStorage(() => AsyncStorage),
      partialize: (state) => ({
        profile: state.profile,
        earnings: state.earnings,
        selectedPeriod: state.selectedPeriod,
      }),
    }
  )
);
