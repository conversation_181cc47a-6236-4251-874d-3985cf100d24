import { create } from 'zustand';

export interface Booking {
  id: string;
  customerId: string;
  providerId?: string;
  serviceId: string;
  addressId: string;
  scheduledAt: Date;
  status: 'PENDING' | 'CONFIRMED' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  createdAt: Date;
  updatedAt: Date;
}

interface BookingState {
  bookings: Booking[];
  currentBooking: Booking | null;
  isLoading: boolean;
  setBookings: (bookings: Booking[]) => void;
  addBooking: (booking: Booking) => void;
  updateBooking: (id: string, updates: Partial<Booking>) => void;
  setCurrentBooking: (booking: Booking | null) => void;
  setLoading: (isLoading: boolean) => void;
}

export const useBookingStore = create<BookingState>((set) => ({
  bookings: [],
  currentBooking: null,
  isLoading: false,
  setBookings: (bookings) => set({ bookings }),
  addBooking: (booking) => set((state) => ({ 
    bookings: [...state.bookings, booking] 
  })),
  updateBooking: (id, updates) => set((state) => ({
    bookings: state.bookings.map(booking => 
      booking.id === id ? { ...booking, ...updates } : booking
    ),
    currentBooking: state.currentBooking?.id === id 
      ? { ...state.currentBooking, ...updates }
      : state.currentBooking
  })),
  setCurrentBooking: (booking) => set({ currentBooking: booking }),
  setLoading: (isLoading) => set({ isLoading }),
})); 