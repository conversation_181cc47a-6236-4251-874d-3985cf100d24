import { create } from 'zustand';

export interface Provider {
  id: string;
  name: string;
  avatar: string;
  location: string;
  rating: number;
  totalReviews: number;
  totalHired: number;
  experience: string;
  description: string;
  isPremium: boolean;
  isBasic: boolean;
  reviews: Review[];
}

export interface Review {
  id: string;
  userName: string;
  userAvatar: string;
  rating: number;
  comment: string;
  date: string;
}

export interface BookingService {
  id: string;
  name: string;
  icon: string;
  price: number;
  description?: string;
  image?: string;
}

export interface BookingState {
  // Step 1: Service Selection
  selectedService: BookingService | null;
  
  // Step 2: Time & Date
  selectedDate: string | null;
  selectedTime: string | null;
  isAutoAssign: boolean;
  selectedProvider: Provider | null;
  jobInfo: string;
  frequency: 'one-time' | 'weekly' | 'monthly';
  
  // Step 3: Confirm & Review
  hours: number;
  cleaners: number;
  needsMaterials: boolean;
  
  // Step 4: Payment
  location: {
    address: string;
    coordinates: { lat: number; lng: number };
  } | null;
  discountCode: string;
  discountAmount: number;
  paymentMethod: 'mastercard' | 'bizumx' | 'applepay' | null;
  
  // Calculated values
  subtotal: number;
  taxes: number;
  total: number;
}

export interface BookingActions {
  // Service selection
  setSelectedService: (service: BookingService) => void;
  
  // Time & Date
  setDateTime: (date: string, time: string) => void;
  setAutoAssign: (autoAssign: boolean) => void;
  setSelectedProvider: (provider: Provider | null) => void;
  setJobInfo: (info: string) => void;
  setFrequency: (frequency: 'one-time' | 'weekly' | 'monthly') => void;
  
  // Confirm & Review
  setHours: (hours: number) => void;
  setCleaners: (cleaners: number) => void;
  setNeedsMaterials: (needs: boolean) => void;
  
  // Payment
  setLocation: (location: { address: string; coordinates: { lat: number; lng: number } }) => void;
  setDiscountCode: (code: string) => void;
  applyDiscount: (amount: number) => void;
  setPaymentMethod: (method: 'mastercard' | 'bizumx' | 'applepay') => void;
  
  // Calculations
  calculateTotals: () => void;
  
  // Reset
  resetBooking: () => void;
}

const initialState: BookingState = {
  selectedService: null,
  selectedDate: null,
  selectedTime: null,
  isAutoAssign: true,
  selectedProvider: null,
  jobInfo: '',
  frequency: 'one-time',
  hours: 2,
  cleaners: 1,
  needsMaterials: true,
  location: null,
  discountCode: '',
  discountAmount: 0,
  paymentMethod: null,
  subtotal: 200,
  taxes: 0,
  total: 200,
};

export const useBookingStore = create<BookingState & BookingActions>((set, get) => ({
  ...initialState,
  
  setSelectedService: (service) => {
    set({ selectedService: service, subtotal: service.price });
    get().calculateTotals();
  },
  
  setDateTime: (date, time) => set({ selectedDate: date, selectedTime: time }),
  setAutoAssign: (autoAssign) => set({ isAutoAssign: autoAssign }),
  setSelectedProvider: (provider) => set({ selectedProvider: provider }),
  setJobInfo: (info) => set({ jobInfo: info }),
  setFrequency: (frequency) => set({ frequency }),
  
  setHours: (hours) => {
    set({ hours });
    get().calculateTotals();
  },
  setCleaners: (cleaners) => {
    set({ cleaners });
    get().calculateTotals();
  },
  setNeedsMaterials: (needs) => {
    set({ needsMaterials: needs });
    get().calculateTotals();
  },
  
  setLocation: (location) => set({ location }),
  setDiscountCode: (code) => set({ discountCode: code }),
  applyDiscount: (amount) => {
    set({ discountAmount: amount });
    get().calculateTotals();
  },
  setPaymentMethod: (method) => set({ paymentMethod: method }),
  
  calculateTotals: () => {
    const state = get();
    const basePrice = state.selectedService?.price || 200;
    const hourMultiplier = state.hours / 2; // Base price is for 2 hours
    const cleanerMultiplier = state.cleaners;
    const materialsExtra = state.needsMaterials ? 0 : 0; // No extra cost for materials
    
    const subtotal = (basePrice * hourMultiplier * cleanerMultiplier) + materialsExtra;
    const discountedSubtotal = subtotal - state.discountAmount;
    const taxes = 0; // No taxes in this example
    const total = discountedSubtotal + taxes;
    
    set({ subtotal: discountedSubtotal, taxes, total });
  },
  
  resetBooking: () => set(initialState),
}));
