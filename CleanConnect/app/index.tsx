import React, { useEffect } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import { useAuthStore } from '../src/store/useAuthStore';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { useFonts } from 'expo-font';

export default function Index() {
  const { isAuthenticated, role, isLoading, isInitialized } = useAuthStore();

  // Load Public Sans font
  const [fontsLoaded] = useFonts({
    'PublicSans-Regular': require('../assets/fonts/PublicSans-Regular.ttf'),
    'PublicSans-Bold': require('../assets/fonts/PublicSans-Bold.ttf'),
    'PublicSans-SemiBold': require('../assets/fonts/PublicSans-SemiBold.ttf'),
    // Add more weights/styles if available
  });

  useEffect(() => {
    // Only navigate when auth is initialized and fonts are loaded
    if (isInitialized && fontsLoaded && !isLoading) {
      if (!isAuthenticated) {
        // Navigate to onboarding if not authenticated
        router.replace('/onboarding');
      } else {
        // Navigate based on user role
        if (role === 'CUSTOMER') {
          router.replace('/customer/home');
        } else if (role === 'PROVIDER') {
          router.replace('/provider/dashboard');
        } else {
          // Default to onboarding if role is not set
          router.replace('/onboarding');
        }
      }
    }
  }, [isAuthenticated, role, isLoading, isInitialized, fontsLoaded]);

  // Wait for fonts to load
  if (!fontsLoaded) {
    return null;
  }

  // Show loading screen while determining route
  return (
    <View style={styles.container}>
      <Text style={styles.title}>CleanConnect</Text>
      <Text style={styles.subtitle}>Loading...</Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.white,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: typography.sizes.display,
    fontWeight: typography.weights.bold,
    color: colors.primary,
    marginBottom: spacing.sm,
    fontFamily: 'PublicSans-Bold',
  },
  subtitle: {
    fontSize: typography.sizes.lg,
    color: colors.gray[600],
    fontFamily: 'PublicSans-Regular',
  },
});
