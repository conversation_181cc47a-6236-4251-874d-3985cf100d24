import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, TouchableOpacity, ScrollView } from 'react-native';
import { router } from 'expo-router';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { Button } from '../../src/components/common/Button';
import { useAuthStore } from '../../src/store/useAuthStore';
import { CustomerNavigation } from '../../src/features/customer/components/CustomerNavigation';

// Temporary Customer Profile Screen
const CustomerProfileScreen: React.FC = () => {
  const { logout } = useAuthStore();

  const handleLogout = () => {
    logout();
    router.replace('/onboarding');
  };

  const profileOptions = [
    { id: '1', title: 'Personal Information', icon: '👤', route: '/customer/profile/personal' },
    { id: '2', title: 'Addresses', icon: '📍', route: '/customer/profile/addresses' },
    { id: '3', title: 'Payment Methods', icon: '💳', route: '/customer/profile/payment' },
    { id: '4', title: 'Notification Settings', icon: '🔔', route: '/customer/notification-settings' },
    { id: '5', title: 'Help & Support', icon: '❓', route: '/customer/profile/support' },
    { id: '6', title: 'Terms & Privacy', icon: '📄', route: '/customer/profile/terms' },
  ];

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.profileInfo}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>JD</Text>
            </View>
            <View style={styles.userInfo}>
              <Text style={styles.userName}>John Doe</Text>
              <Text style={styles.userEmail}><EMAIL></Text>
              <Text style={styles.userPhone}>+************</Text>
            </View>
          </View>
        </View>

        {/* Profile Options */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Account Settings</Text>
          {profileOptions.map((option) => (
            <TouchableOpacity
              key={option.id}
              style={styles.optionItem}
              onPress={() => router.push(option.route as any)}
            >
              <View style={styles.optionLeft}>
                <Text style={styles.optionIcon}>{option.icon}</Text>
                <Text style={styles.optionTitle}>{option.title}</Text>
              </View>
              <Text style={styles.optionArrow}>›</Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <View style={styles.logoutSection}>
          <Button
            title="Logout"
            onPress={handleLogout}
            style={styles.logoutButton}
          />
        </View>
      </ScrollView>
      
      <CustomerNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.white,
    padding: spacing.xl,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  profileInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: spacing.lg,
  },
  avatarText: {
    fontSize: typography.sizes.xl,
    color: colors.white,
    fontWeight: typography.weights.bold,
    fontFamily: typography.fontFamily.bold,
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    fontSize: typography.sizes.xl,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  userEmail: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  userPhone: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  section: {
    backgroundColor: colors.white,
    margin: spacing.lg,
    borderRadius: 12,
    padding: spacing.lg,
  },
  sectionTitle: {
    fontSize: typography.sizes.lg,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.md,
    fontFamily: typography.fontFamily.semibold,
  },
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: spacing.md,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  optionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionIcon: {
    fontSize: 20,
    marginRight: spacing.md,
  },
  optionTitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[900],
    fontFamily: typography.fontFamily.regular,
  },
  optionArrow: {
    fontSize: 20,
    color: colors.gray[400],
  },
  logoutSection: {
    padding: spacing.xl,
  },
  logoutButton: {
    backgroundColor: colors.error,
  },
});

export default function CustomerProfilePage() {
  return <CustomerProfileScreen />;
}
