import React from 'react';
import { View, Text, StyleSheet, SafeAreaView, ScrollView } from 'react-native';
import { colors } from '@/constants/colors';
import { spacing } from '@/constants/spacing';
import { typography } from '@/constants/typography';
import { ProviderNavigation } from '../../src/features/provider/components/ProviderNavigation';

// Temporary Provider Notifications Screen
const ProviderNotificationsScreen: React.FC = () => {
  const mockNotifications = [
    {
      id: '1',
      type: 'new_booking',
      title: 'New Booking Request',
      message: 'You have a new house cleaning booking request for tomorrow at 2:00 PM',
      time: '5 min ago',
      read: false,
    },
    {
      id: '2',
      type: 'booking_update',
      title: 'Booking Confirmed',
      message: 'Your booking with <PERSON> has been confirmed',
      time: '1 hour ago',
      read: true,
    },
    {
      id: '3',
      type: 'payment',
      title: 'Payment Received',
      message: 'You received $89 for completed house cleaning service',
      time: '2 hours ago',
      read: true,
    },
  ];

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'new_booking':
        return '📋';
      case 'booking_update':
        return '✅';
      case 'payment':
        return '💰';
      default:
        return '📢';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.headerTitle}>Notifications</Text>
          <Text style={styles.headerSubtitle}>Stay updated with your bookings</Text>
        </View>

        {/* Notifications List */}
        <View style={styles.notificationsList}>
          {mockNotifications.map((notification) => (
            <View
              key={notification.id}
              style={[
                styles.notificationCard,
                !notification.read && styles.unreadNotification,
              ]}
            >
              <View style={styles.notificationHeader}>
                <View style={styles.notificationInfo}>
                  <Text style={styles.notificationIcon}>
                    {getNotificationIcon(notification.type)}
                  </Text>
                  <View style={styles.notificationText}>
                    <Text style={styles.notificationTitle}>{notification.title}</Text>
                    <Text style={styles.notificationMessage}>{notification.message}</Text>
                  </View>
                </View>
                <View style={styles.notificationMeta}>
                  <Text style={styles.notificationTime}>{notification.time}</Text>
                  {!notification.read && <View style={styles.unreadDot} />}
                </View>
              </View>
            </View>
          ))}
        </View>
      </ScrollView>
      
      <ProviderNavigation />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.neutral,
  },
  content: {
    flex: 1,
  },
  header: {
    backgroundColor: colors.white,
    paddingHorizontal: spacing.xl,
    paddingVertical: spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.gray[200],
  },
  headerTitle: {
    fontSize: typography.sizes.xxl,
    fontWeight: typography.weights.bold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.bold,
  },
  headerSubtitle: {
    fontSize: typography.sizes.md,
    color: colors.gray[600],
    fontFamily: typography.fontFamily.regular,
  },
  notificationsList: {
    padding: spacing.lg,
  },
  notificationCard: {
    backgroundColor: colors.white,
    borderRadius: 12,
    marginBottom: spacing.md,
    shadowColor: colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  unreadNotification: {
    borderLeftWidth: 4,
    borderLeftColor: colors.primary,
  },
  notificationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: spacing.lg,
  },
  notificationInfo: {
    flexDirection: 'row',
    flex: 1,
    marginRight: spacing.md,
  },
  notificationIcon: {
    fontSize: 24,
    marginRight: spacing.md,
  },
  notificationText: {
    flex: 1,
  },
  notificationTitle: {
    fontSize: typography.sizes.md,
    fontWeight: typography.weights.semibold,
    color: colors.gray[900],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.semibold,
  },
  notificationMessage: {
    fontSize: typography.sizes.sm,
    color: colors.gray[600],
    lineHeight: typography.lineHeights.normal,
    fontFamily: typography.fontFamily.regular,
  },
  notificationMeta: {
    alignItems: 'flex-end',
  },
  notificationTime: {
    fontSize: typography.sizes.xs,
    color: colors.gray[500],
    marginBottom: spacing.xs,
    fontFamily: typography.fontFamily.regular,
  },
  unreadDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: colors.primary,
  },
});

export default function ProviderNotificationsPage() {
  return <ProviderNotificationsScreen />;
}
