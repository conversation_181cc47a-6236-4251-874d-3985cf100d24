import React, { useEffect, useState } from 'react';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { useAuthStore } from '../src/store/useAuthStore';
import { NotificationToast } from '../src/components/common/NotificationToast';
import { useNotificationStore } from '../src/store/useNotificationStore';
import { SplashScreen } from '../src/components/common/SplashScreen';
import SafeScreen from '../src/components/common/SafeScreen';
import { auth } from '../src/lib/supabase';

export default function RootLayout() {
  const {
    isAuthenticated,
    role,
    isLoading,
    isInitialized,
    initializeAuth,
    setUser,
    setRole,
    setAuthenticated
  } = useAuthStore();
  const { notifications, markAsRead, initializePushNotifications } = useNotificationStore();
  const [currentNotification, setCurrentNotification] = useState<any>(null);

  useEffect(() => {
    // Initialize authentication state on app start
    const initAuth = async () => {
      await initializeAuth();
    };

    initAuth();
  }, [initializeAuth]);

  useEffect(() => {
    // Listen for auth state changes from Supabase
    const { data: { subscription } } = auth.onAuthStateChange(async (event, session) => {
      console.log('Auth state changed:', event, session?.user?.id);

      if (event === 'SIGNED_IN' && session?.user) {
        // User signed in
        setUser(session.user);
        setAuthenticated(true);
        // Role should be maintained from the store or set during login
      } else if (event === 'SIGNED_OUT') {
        // User signed out
        setUser(null);
        setRole(null);
        setAuthenticated(false);
      }
    });

    return () => {
      subscription?.unsubscribe();
    };
  }, [setUser, setRole, setAuthenticated]);

  useEffect(() => {
    // Initialize push notifications when user is authenticated
    if (isAuthenticated) {
      const userId = 'user-123'; // In real app, get from auth store
      initializePushNotifications(userId);
    }
  }, [isAuthenticated, initializePushNotifications]);

  useEffect(() => {
    // Show the most recent unread notification
    const unreadNotification = notifications.find(n => !n.read);
    if (unreadNotification && !currentNotification) {
      setCurrentNotification(unreadNotification);
      markAsRead(unreadNotification.id);
    }
  }, [notifications, currentNotification, markAsRead]);

  const handleNotificationClose = () => {
    setCurrentNotification(null);
  };

  if (isLoading || !isInitialized) {
    return <SplashScreen />;
  }

  return (
    <SafeAreaProvider>
      <SafeScreen>
        <StatusBar style="auto" />

        {/* Notification Toast */}
        <NotificationToast
          visible={!!currentNotification}
          title={currentNotification?.title || ''}
          message={currentNotification?.message || ''}
          type={currentNotification?.type === 'booking_update' ? 'info' : 'info'}
          onClose={handleNotificationClose}
        />

        <Stack
          screenOptions={{
            headerShown: false,
          }}
        >
        <Stack.Screen
          name="index"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="onboarding"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="auth/phone"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="auth/email"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/home"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/booking/service-selection"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/booking/date-time"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/booking/address"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/booking/confirmation"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/bookings"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/chat"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/notifications"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/profile"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/notification-settings"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/search"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/service-detail/[serviceId]"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="customer/category/[categoryId]"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/dashboard"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/bookings"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/earnings"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/notifications"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/profile"
          options={{
            headerShown: false,
          }}
        />
        <Stack.Screen
          name="provider/send-notification"
          options={{
            headerShown: false,
          }}
        />
        </Stack>
      </SafeScreen>
    </SafeAreaProvider>
  );
}
