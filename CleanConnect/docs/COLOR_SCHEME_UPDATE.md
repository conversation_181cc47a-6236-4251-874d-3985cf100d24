# 🎨 CleanConnect Color Scheme Update

## Overview
Updated the color scheme to align with the brand guidelines specified in CONTEX.md.

## Brand Color Mapping

### Primary Colors
| Role | Old Color | New Color | Description |
|------|-----------|-----------|-------------|
| Primary | `#FF6B35` (Orange) | `#6FD1FF` (Sky Blue) | Main brand color |
| Secondary | `#4ECDC4` (Teal) | `#A2E2BD` (Mint Green) | Secondary brand color |
| Accent | `#84D3F2` (Light Cyan) | `#84D3F2` (Light Cyan) | Unchanged - already aligned |

### Status Colors
| Role | Old Color | New Color | Description |
|------|-----------|-----------|-------------|
| Success | `#10B981` | `#4CAF50` | Status indicator |
| Warning | `#F59E0B` | `#FFD43B` | Bright yellow for alerts |

### Neutral Colors
| Role | Old Color | New Color | Description |
|------|-----------|-----------|-------------|
| Neutral | `#FFF5F0` | `#F9F9F9` | Background light |

## New Brand-Specific Colors

### Brand Palette
- **Sky Blue**: `#6FD1FF` - Primary brand color
- **Mint Green**: `#A2E2BD` - Secondary brand color  
- **Light Cyan**: `#84D3F2` - Accent color
- **Bright Yellow**: `#FFD43B` - Warning/attention color
- **Clean White**: `#FFFFFF` - Pure clean white
- **Soft Gray**: `#F9F9F9` - Neutral background

### Service Category Colors
- **Cleaning**: `#6FD1FF` (Sky Blue)
- **Laundry**: `#A2E2BD` (Mint Green)
- **Deep Cleaning**: `#84D3F2` (Light Cyan)
- **Maintenance**: `#FFD43B` (Bright Yellow)

## Component Updates

### Buttons
- **Primary Button**: Now uses Sky Blue (`#6FD1FF`)
- **Secondary Button**: Now uses Mint Green (`#A2E2BD`)

### Cards
- **Service Cards**: Enhanced with brand-specific styling
- **Shadows**: Updated to use Sky Blue tints

### Screens
- **Background**: Updated to use new neutral color (`#F9F9F9`)

## Usage Examples

```typescript
// Import the updated colors
import { colors } from '@/constants/colors';

// Use brand colors
backgroundColor: colors.primary,        // Sky Blue
backgroundColor: colors.secondary,      // Mint Green
backgroundColor: colors.accent,         // Light Cyan

// Use brand-specific colors
backgroundColor: colors.brand.skyBlue,
backgroundColor: colors.brand.mintGreen,

// Use service category colors
backgroundColor: colors.categories.cleaning,
backgroundColor: colors.categories.laundry,
```

## Impact
- ✅ All colors now align with CONTEX.md specifications
- ✅ Brand identity is consistent across the app
- ✅ Service categories have distinct visual identity
- ✅ Maintains accessibility and contrast standards
- ✅ Backward compatible with existing components
