# CleanConnect Backend

A centralized backend system for the CleanConnect platform, serving web-based admin dashboard and mobile apps for customers and providers.

## Tech Stack

- **Backend Framework**: Express.js
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Hybrid (Supabase + JWT) with role-based access control
- **Mobile Authentication**: Supabase (OTP for customers, email/password for providers)
- **Admin Authentication**: JWT tokens
- **Email & Notifications**: NodeMailer for OTPs and email triggers
- **Security**: Helmet, CORS, Rate Limiting, Input Validation

## Features

- Multi-role user management (Customer, Provider, Admin)
- Profile management system
- Booking flow with status tracking
- Notification system
- Review and rating system
- Admin panel API
- Address and location management
- Service area management for providers
- Service statistics tracking
- API documentation with Swagger

## Project Structure

```
src/
├── controllers/     # Request handlers
├── middlewares/     # Express middlewares
├── routes/          # API routes
├── services/        # Business logic
├── utils/           # Utility functions
└── server.js        # Entry point
prisma/
└── schema.prisma    # Database schema


### Authentication

- `POST /api/auth/register` - Register a new user
- `POST /api/auth/login` - Login user
- `POST /api/auth/verify-email` - Verify email with OTP
- `POST /api/auth/refresh-token` - Refresh access token
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with OTP

### User Management

- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile
- `PUT /api/users/password` - Update user password
- `PUT /api/users/provider-profile` - Update provider profile
- `GET /api/users/providers` - Get all providers
- `GET /api/users/providers/:id` - Get provider by ID

### Address Management

- `POST /api/addresses` - Create a new address
- `GET /api/addresses` - Get all addresses for a user
- `GET /api/addresses/:id` - Get address by ID
- `PUT /api/addresses/:id` - Update address
- `DELETE /api/addresses/:id` - Delete address
- `PUT /api/addresses/:id/primary` - Set address as primary

### Booking Management

- `POST /api/bookings` - Create a new booking
- `GET /api/bookings/customer` - Get all bookings for a customer
- `GET /api/bookings/provider` - Get all bookings for a provider
- `GET /api/bookings/:id` - Get booking by ID
- `PUT /api/bookings/:id/status` - Update booking status
- `PUT /api/bookings/:id/assign` - Assign provider to booking

### Notification Management

- `GET /api/notifications` - Get user notifications
- `PUT /api/notifications/:id/read` - Mark notification as read
- `PUT /api/notifications/read-all` - Mark all notifications as read
- `DELETE /api/notifications/:id` - Delete notification
- `GET /api/notifications/unread-count` - Get unread notification count

### Review Management

- `POST /api/reviews` - Create a new review
- `GET /api/reviews/provider/:id` - Get provider reviews
- `GET /api/reviews/customer` - Get customer reviews
- `GET /api/reviews/:id` - Get review by ID
- `PUT /api/reviews/:id/visibility` - Update review visibility

### Service Management

- `GET /api/services` - Get all services
- `GET /api/services/:id` - Get service by ID
- `POST /api/services` - Create a new service
- `PUT /api/services/:id` - Update service
- `POST /api/services/provider` - Add service to provider
- `GET /api/services/provider` - Get provider services
- `PUT /api/services/provider/:id` - Update provider service
- `DELETE /api/services/provider/:id` - Remove provider service

### Availability Management

- `POST /api/availability` - Create provider availability
- `GET /api/availability` - Get provider availability
- `PUT /api/availability/:id` - Update provider availability
- `DELETE /api/availability/:id` - Delete provider availability

### Service Area Management

- `POST /api/service-areas` - Create service area
- `GET /api/service-areas` - Get provider service areas
- `GET /api/service-areas/:id` - Get service area by ID
- `PUT /api/service-areas/:id` - Update service area
- `DELETE /api/service-areas/:id` - Delete service area

### Statistics Management

- `GET /api/statistics/services` - Get all service statistics
- `GET /api/statistics/services/:id` - Get service statistics by ID

### Admin Management

- `GET /api/admin/users` - Get all users
- `GET /api/admin/users/:id` - Get user by ID
- `PUT /api/admin/users/:id/status` - Update user status
- `PUT /api/admin/providers/:id/verify` - Verify provider
- `GET /api/admin/bookings` - Get all bookings
- `GET /api/admin/reviews` - Get all reviews
- `GET /api/admin/dashboard` - Get dashboard statistics