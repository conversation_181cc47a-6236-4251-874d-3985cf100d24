# 📘 CleanConnect – Project Documentation

**Digitalizing Domestic Services with a Centralized, Role-Based Mobile App for The Gambia**

CleanConnect is a modern on-demand home service platform designed to seamlessly connect homeowners with trusted, local service providers for tasks like cleaning, laundry. Our mission is to professionalize and digitize domestic work in emerging markets, starting with The Gambia. CleanConnect offers a user-friendly, role-based mobile app for customers to browse, book, and manage services, while empowering service providers with flexible job opportunities and profile visibility.



## 🛠️ Tech Stack Summary

| Layer         | Stack / Tooling                       

| **Frontend**  | React Native (TypeScript, Expo)  

                    
| **Auth**      | Supabase Auth (OTP-based: Phone Number/Email+Password)|

| **Backend**   | ExpressJS, Prisma ORM, PostgreSQL       
             
| **Realtime**  | Supabase Realtime (Job Broadcasting), Supabase Storage|

| **Database**  | PostgreSQL (via Prisma ORM)               
           
| **State management**     | Zustand    
                                          
| **Navigation**| Expo Navigation     
                               
| **Arch.**     | Feature-Based Architecture              
             



## 🎨 Brand Color Scheme

| Color Role | Hex Code  | Description     |
| ---------- | --------- | -------------- |
| Primary    | `#6FD1FF` | Sky Blue       |
| Secondary  | `#A2E2BD` | Mint Green     |
| Accent     | `#84D3F2` | Light Cyan     |
| Warning    | `#FFD43B` | Bright Yellow  |
| Success    | `#4CAF50` | Status Indicator|
| Neutral    | `#F9F9F9` | Background Light|


## 📦 Folder Structure (Feature-Based)

src/
├── assets/
├── constants/
│   ├── colors.ts
│   ├── fonts.ts
│   ├── spacing.ts
│   └── typography.ts
├── components/
│   ├── common/
│   └── bookings/
├── features/
│   ├── auth/
│   ├── customer/
│   │   ├── components/
│   │   ├── screens/
│   │   │   ├── HomeScreen/
│   │   │   ├── BookingScreen/
│   │   │   ├── BookingDetailScreen/
│   │   │   └── ...
│   │   ├── services/
│   │   ├── styles/
│   │   └── types.ts
│   └── provider/

Make sure all screen styles are extracted and put in the styles/ directory so that screens files will be free of services logics and styles


## 👥 User Roles & Access

| Role      | Access Areas                                         |
| --------- | ---------------------------------------------------- |
| Customer  | Onboarding → Browse Services → Book → Review         |
| Provider  | Onboarding → Manage Profile → Accept Jobs → Complete Tasks |



## 🔁 User Flows

### Customer

1. Onboarding (3 slides)
2. Role Selection: **Customer**
3. Home Screen: Browse services, quick booking shortcuts, popular deals
4. Booking Process:  
   - Select service → Date/Time → Address  
   - Choose Provider or Auto-Assign  
   - Login/Register via OTP  
   - Booking Summary → Confirmation  
   - Track Booking → Submit Review

### Provider

1. Onboarding (3 slides)
2. Role Selection: **Provider**
3. Login/Register via Email & Password
4. Profile Completion
5. Dashboard: View Jobs → Accept Booking → Mark as Complete
6. Ratings & Reviews



## 🔄 Booking Lifecycle

1. Customer fills form → Booking request created
2. Supabase broadcasts job in real-time
3. Available providers can accept
4. Customer is notified of booking status
5. Admin (manual/automated) validates and confirms
6. Job scheduled → moved to "In Progress"
7. On completion → marked "Completed" → triggers review flow



## 🔐 Authentication Flow (Supabase OTP)

1. Phone number entry → 6-digit OTP sent
2. Supabase Auth validates → JWT returned
3. Session maintained locally
4. Login tied to user role (Customer or Provider)



## 🌐 Backend API Overview (ExpressJS)
refer to  BackenEnpointDoc.md file 



## 📤 Realtime & Job Broadcasting (Supabase)

- Uses Supabase Channels to notify providers of:
  - New job requests
  - Status updates (assigned, completed)
- Optional filters: availability & proximity



## 🧠 Smart Matching Logic (Future)

| Match Factor   | Notes                                 |
| -------------- | ------------------------------------- |
| Availability   | From provider availability table      |
| Location       | Based on service area and address     |
| Rating/Exp.    | Boost high-performers                 |



## 🔐 Security & Roles

- JWT from Supabase secured with role-based middleware
- Database-level access control:
  - Customer booking history
  - Provider job access
  - Admin approval



## ✅ Admin Panel Compatibility

- Built with Next.js + TailwindCSS
- Connects to same ExpressJS backend
- Fully RESTful endpoints across roles



## 🚀 MVP Launch Goals

| Area           | Status      |
| -------------- | ---------- |
| Core Features  | ✅ Designed |
| Authentication | ✅ OTP Working |
| Bookings       | ✅ Functional |
| Realtime Engine| 🟡 Testing  |
| Admin Review   | 🟢 Partial  |

---

## 📌 Final Notes

- Ensure database schema is always synced (Prisma)
- Maintain `.env` with correct Supabase keys, DB URL, and app secrets
- Use mock data fallback only in development
- Auto-seed services during boot for consistent experience

---

Let me know if you want this saved as a file or need further customization!

## 🗄️ Database Schema (Prisma)

Below is the inferred Prisma schema for CleanConnect, based on the backend API and features. Adjust as needed for your implementation.

```prisma
// User roles: Customer, Provider, Admin
model User {
  id             String   @id @default(uuid())
  email          String   @unique
  phone          String?  @unique
  password       String
  role           Role
  isVerified     Boolean  @default(false)
  status         UserStatus @default(ACTIVE)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  profile        Profile?
  providerProfile ProviderProfile?
  addresses      Address[]
  bookingsAsCustomer Booking[] @relation("CustomerBookings")
  bookingsAsProvider Booking[] @relation("ProviderBookings")
  notifications  Notification[]
  reviewsGiven   Review[] @relation("ReviewsGiven")
  reviewsReceived Review[] @relation("ReviewsReceived")
}

enum Role {
  CUSTOMER
  PROVIDER
  ADMIN
}

enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
}

model Profile {
  id        String  @id @default(uuid())
  user      User    @relation(fields: [userId], references: [id])
  userId    String  @unique
  fullName  String
  avatarUrl String?
  // Add more profile fields as needed
}

model ProviderProfile {
  id           String   @id @default(uuid())
  user         User     @relation(fields: [userId], references: [id])
  userId       String   @unique
  bio          String?
  verified     Boolean  @default(false)
  rating       Float?   @default(0)
  serviceAreas ServiceArea[]
  services     ProviderService[]
  availability Availability[]
}

model Address {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  label     String
  address1  String
  address2  String?
  city      String
  region    String
  country   String
  postalCode String?
  isPrimary Boolean  @default(false)
}

model Booking {
  id           String   @id @default(uuid())
  customer     User     @relation("CustomerBookings", fields: [customerId], references: [id])
  customerId   String
  provider     User?    @relation("ProviderBookings", fields: [providerId], references: [id])
  providerId   String?
  service      Service  @relation(fields: [serviceId], references: [id])
  serviceId    String
  address      Address  @relation(fields: [addressId], references: [id])
  addressId    String
  scheduledAt  DateTime
  status       BookingStatus @default(PENDING)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  review       Review?
}

enum BookingStatus {
  PENDING
  CONFIRMED
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

model Notification {
  id        String   @id @default(uuid())
  user      User     @relation(fields: [userId], references: [id])
  userId    String
  message   String
  type      String
  isRead    Boolean  @default(false)
  createdAt DateTime @default(now())
}

model Review {
  id          String   @id @default(uuid())
  booking     Booking  @relation(fields: [bookingId], references: [id])
  bookingId   String   @unique
  reviewer    User     @relation("ReviewsGiven", fields: [reviewerId], references: [id])
  reviewerId  String
  reviewee    User     @relation("ReviewsReceived", fields: [revieweeId], references: [id])
  revieweeId  String
  rating      Int
  comment     String?
  visible     Boolean  @default(true)
  createdAt   DateTime @default(now())
}

model Service {
  id          String   @id @default(uuid())
  name        String
  description String?
  price       Float
  providerServices ProviderService[]
  bookings    Booking[]
  statistics  ServiceStatistics[]
}

model ProviderService {
  id         String   @id @default(uuid())
  provider   ProviderProfile @relation(fields: [providerId], references: [id])
  providerId String
  service    Service  @relation(fields: [serviceId], references: [id])
  serviceId  String
  price      Float?
  // Add more fields as needed
}

model Availability {
  id         String   @id @default(uuid())
  provider   ProviderProfile @relation(fields: [providerId], references: [id])
  providerId String
  dayOfWeek  Int
  startTime  String
  endTime    String
}

model ServiceArea {
  id         String   @id @default(uuid())
  provider   ProviderProfile @relation(fields: [providerId], references: [id])
  providerId String
  areaName   String
  city       String
  region     String
  country    String
}

model ServiceStatistics {
  id         String   @id @default(uuid())
  service    Service  @relation(fields: [serviceId], references: [id])
  serviceId  String
  totalBookings Int   @default(0)
  avgRating   Float? @default(0)
}

// Admin management is handled via the User model with role = ADMIN
```
